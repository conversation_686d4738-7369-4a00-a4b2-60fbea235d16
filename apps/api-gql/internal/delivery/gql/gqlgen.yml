# Where are all the schema files located? globs are supported eg  src/**/*.graphql
schema:
  - apps/api-gql/internal/delivery/gql/schema/*.graphql

# Where should the generated server code go?
exec:
  filename: apps/api-gql/internal/delivery/gql/graph/generated.go
  package: graph

# Uncomment to enable federation
# federation:
#   filename: graph/federation.go
#   package: graph

# Where should any generated models go?
model:
  filename: apps/api-gql/internal/delivery/gql/gqlmodel/gqlmodel.go
  package: gqlmodel

# Where should the resolver implementations go?
resolver:
  layout: follow-schema
  dir: apps/api-gql/internal/delivery/gql/resolvers
  package: resolvers
  filename_template: "{name}.resolver.go"
  # Optional: turn on to not generate template comments above resolvers
  # omit_template_comment: false

# Optional: turn on use ` + "`" + `gqlgen:"fieldName"` + "`" + ` tags in your models
# struct_tag: json

# Optional: turn on to use []Thing instead of []*Thing
omit_slice_element_pointers: true

# Optional: turn on to omit Is<Name>() methods to interface and unions
# omit_interface_checks : true

# Optional: turn on to skip generation of ComplexityRoot struct content and Complexity function
# omit_complexity: false

# Optional: turn on to not generate any file notice comments in generated files
# omit_gqlgen_file_notice: false

# Optional: turn on to exclude the gqlgen version in the generated file notice. No effect if `omit_gqlgen_file_notice` is true.
# omit_gqlgen_version_in_file_notice: false

# Optional: turn off to make struct-type struct fields not use pointers
# e.g. type Thing struct { FieldA OtherThing } instead of { FieldA *OtherThing }
# struct_fields_always_pointers: true

# Optional: turn off to make resolvers return values instead of pointers for structs
# resolvers_always_return_pointers: true

# Optional: turn on to return pointers instead of values in unmarshalInput
# return_pointers_in_unmarshalinput: false

# Optional: wrap nullable input fields with Omittable
nullable_input_omittable: true

# Optional: set to speed up generation time by not performing a final validation pass.
# skip_validation: true

# Optional: set to skip running `go mod tidy` when generating server code
skip_mod_tidy: true

# gqlgen will search for any type names in the schema in these go packages
# if they match it will use them, otherwise it will generate them.
autobind:
#  - "github.com/twirapp/twir/apps/api-gql/graph/model"

# This section declares type mapping between the GraphQL and go type systems
#
# The first line in each type will be used as defaults for resolver arguments and
# modelgen, the others will be allowed when binding to fields. Configure them to
# your liking
models:
  ID:
    model:
      - github.com/99designs/gqlgen/graphql.ID
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  Int:
    model:
      - github.com/99designs/gqlgen/graphql.Int
      - github.com/99designs/gqlgen/graphql.Int64
      - github.com/99designs/gqlgen/graphql.Int32
  DateTime:
    model:
      - github.com/99designs/gqlgen/graphql.Time
  UUID:
    model:
      - github.com/99designs/gqlgen/graphql.UUID
