package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"

	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
)

// GamesEightBallUpdate is the resolver for the gamesEightBallUpdate field.
func (r *mutationResolver) GamesEightBallUpdate(ctx context.Context, opts gqlmodel.EightBallGameOpts) (*gqlmodel.EightBallGame, error) {
	return r.gamesUpdateEightBall(ctx, opts)
}

// GamesDuelUpdate is the resolver for the gamesDuelUpdate field.
func (r *mutationResolver) GamesDuelUpdate(ctx context.Context, opts gqlmodel.DuelGameOpts) (*gqlmodel.DuelGame, error) {
	return r.gamesUpdateDuel(ctx, opts)
}

// GamesRussianRouletteUpdate is the resolver for the gamesRussianRouletteUpdate field.
func (r *mutationResolver) GamesRussianRouletteUpdate(ctx context.Context, opts gqlmodel.RussianRouletteGameOpts) (*gqlmodel.RussianRouletteGame, error) {
	return r.gamesUpdateRussianRoulette(ctx, opts)
}

// GamesSeppukuUpdate is the resolver for the gamesSeppukuUpdate field.
func (r *mutationResolver) GamesSeppukuUpdate(ctx context.Context, opts gqlmodel.SeppukuGameOpts) (*gqlmodel.SeppukuGame, error) {
	return r.gamesUpdateSeppuku(ctx, opts)
}

// GamesVotebanUpdate is the resolver for the gamesVotebanUpdate field.
func (r *mutationResolver) GamesVotebanUpdate(ctx context.Context, opts gqlmodel.VotebanGameOpts) (*gqlmodel.VotebanGame, error) {
	return r.gamesUpdateVoteban(ctx, opts)
}

// GamesEightBall is the resolver for the gamesEightBall field.
func (r *queryResolver) GamesEightBall(ctx context.Context) (*gqlmodel.EightBallGame, error) {
	return r.gamesGetEightBall(ctx)
}

// GamesDuel is the resolver for the gamesDuel field.
func (r *queryResolver) GamesDuel(ctx context.Context) (*gqlmodel.DuelGame, error) {
	return r.gamesGetDuel(ctx)
}

// GamesRussianRoulette is the resolver for the gamesRussianRoulette field.
func (r *queryResolver) GamesRussianRoulette(ctx context.Context) (*gqlmodel.RussianRouletteGame, error) {
	return r.gamesGetRussianRoulette(ctx)
}

// GamesSeppuku is the resolver for the gamesSeppuku field.
func (r *queryResolver) GamesSeppuku(ctx context.Context) (*gqlmodel.SeppukuGame, error) {
	return r.gamesSeppuku(ctx)
}

// GamesVoteban is the resolver for the gamesVoteban field.
func (r *queryResolver) GamesVoteban(ctx context.Context) (*gqlmodel.VotebanGame, error) {
	return r.gamesVoteban(ctx)
}
