package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/mappers"
	"github.com/twirapp/twir/apps/api-gql/internal/services/seventv_integration"
)

// SevenTvUpdate is the resolver for the sevenTvUpdate field.
func (r *mutationResolver) SevenTvUpdate(ctx context.Context, input gqlmodel.SevenTvUpdateInput) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return false, err
	}

	err = r.deps.SevenTvIntegrationService.CreateOrUpdateSevenTvData(
		ctx,
		seventv_integration.CreateInput{
			ChannelID:                  dashboardID,
			RewardIDForAddEmote:        input.RewardIDForAddEmote.Value(),
			RewardIDForRemoveEmote:     input.RewardIDForRemoveEmote.Value(),
			DeleteEmotesOnlyAddedByApp: input.DeleteEmotesOnlyAddedByApp,
		},
	)
	if err != nil {
		return false, fmt.Errorf("cannot update seven tv data: %w", err)
	}

	return true, nil
}

// SevenTvData is the resolver for the sevenTvData field.
func (r *subscriptionResolver) SevenTvData(ctx context.Context) (<-chan *gqlmodel.SevenTvIntegration, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return nil, err
	}

	channel := make(chan *gqlmodel.SevenTvIntegration, 1)

	go func() {
		defer close(channel)

		for {
			select {
			case <-ctx.Done():
				return
			default:
				data, err := r.deps.SevenTvIntegrationService.GetSevenTvData(ctx, dashboardID)
				if err != nil {
					r.deps.Logger.Error("failed to get seven tv data", slog.Any("err", err))
					time.Sleep(1 * time.Second)
					continue
				}

				channel <- mappers.MapSevenTvIntegrationDataToGql(data)

				time.Sleep(1 * time.Second)
			}
		}
	}()

	return channel, nil
}
