package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"errors"

	data_loader "github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/dataloader"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/graph"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/mappers"
	"github.com/twirapp/twir/apps/api-gql/internal/services/chat_wall"
)

// TwitchProfile is the resolver for the twitchProfile field.
func (r *chatWallLogResolver) TwitchProfile(ctx context.Context, obj *gqlmodel.ChatWallLog) (*gqlmodel.TwirUserTwitchInfo, error) {
	return data_loader.GetHelixUserById(ctx, obj.UserID)
}

// ChatWallSettingsUpdate is the resolver for the chatWallSettingsUpdate field.
func (r *mutationResolver) ChatWallSettingsUpdate(ctx context.Context, opts gqlmodel.ChatWallSettingsUpdateInput) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return false, err
	}

	err = r.deps.ChatWallService.UpdateChannelSettings(
		ctx,
		dashboardID,
		opts.MuteSubscribers,
		opts.MuteVips,
	)
	if err != nil {
		return false, err
	}

	return true, nil
}

// ChatWalls is the resolver for the chatWalls field.
func (r *queryResolver) ChatWalls(ctx context.Context) ([]gqlmodel.ChatWall, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return nil, err
	}

	chatWalls, err := r.deps.ChatWallService.GetChatWalls(ctx, dashboardID)
	if err != nil {
		return nil, err
	}

	converted := make([]gqlmodel.ChatWall, len(chatWalls))
	for idx, wall := range chatWalls {
		converted[idx] = mappers.MapChatWallToGql(wall)
	}

	return converted, nil
}

// ChatWallSettings is the resolver for the chatWallSettings field.
func (r *queryResolver) ChatWallSettings(ctx context.Context) (*gqlmodel.ChatWallSettings, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return nil, err
	}

	settings, err := r.deps.ChatWallService.GetChannelSettings(ctx, dashboardID)
	if err != nil {
		if errors.Is(err, chat_wall.ErrSettingsNotFound) {
			return &gqlmodel.ChatWallSettings{
				MuteSubscribers: true,
				MuteVips:        false,
			}, nil
		}
		return nil, err
	}

	return &gqlmodel.ChatWallSettings{
		MuteSubscribers: settings.MuteSubscribers,
		MuteVips:        settings.MuteVips,
	}, nil
}

// ChatWallLogs is the resolver for the chatWallLogs field.
func (r *queryResolver) ChatWallLogs(ctx context.Context, id string) ([]gqlmodel.ChatWallLog, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if err != nil {
		return nil, err
	}

	logs, err := r.deps.ChatWallService.GetLogs(ctx, dashboardID, id)
	if err != nil {
		return nil, err
	}

	converted := make([]gqlmodel.ChatWallLog, len(logs))
	for idx, log := range logs {
		converted[idx] = mappers.MapChatWallLogToGql(log)
	}

	return converted, nil
}

// ChatWallLog returns graph.ChatWallLogResolver implementation.
func (r *Resolver) ChatWallLog() graph.ChatWallLogResolver { return &chatWallLogResolver{r} }

type chatWallLogResolver struct{ *Resolver }
