package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"log/slog"

	"github.com/goccy/go-json"
	"github.com/google/uuid"
	"github.com/samber/lo"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/mappers"
	"github.com/twirapp/twir/apps/api-gql/internal/entity"
	"github.com/twirapp/twir/apps/api-gql/internal/services/overlays_dudes"
)

// DudesUpdate is the resolver for the dudesUpdate field.
func (r *mutationResolver) DudesUpdate(ctx context.Context, id uuid.UUID, input gqlmodel.DudesOverlaySettingsInput) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if dashboardID == "" {
		return false, err
	}

	updateInput := overlays_dudes.UpdateInput{
		DudeSettings: &overlays_dudes.UpdateDudeSettingsInput{
			Color:          &input.DudeSettings.Color,
			EyesColor:      &input.DudeSettings.EyesColor,
			CosmeticsColor: &input.DudeSettings.CosmeticsColor,
			MaxLifeTime:    &input.DudeSettings.MaxLifeTime,
			Gravity:        &input.DudeSettings.Gravity,
			Scale:          &input.DudeSettings.Scale,
			SoundsEnabled:  &input.DudeSettings.SoundsEnabled,
			SoundsVolume:   &input.DudeSettings.SoundsVolume,
			VisibleName:    &input.DudeSettings.VisibleName,
			GrowTime:       &input.DudeSettings.GrowTime,
			GrowMaxScale:   &input.DudeSettings.GrowMaxScale,
			MaxOnScreen:    &input.DudeSettings.MaxOnScreen,
			DefaultSprite:  &input.DudeSettings.DefaultSprite,
		},
		MessageBoxSettings: &overlays_dudes.UpdateMessageBoxSettingsInput{
			Enabled:      &input.MessageBoxSettings.Enabled,
			BorderRadius: &input.MessageBoxSettings.BorderRadius,
			BoxColor:     &input.MessageBoxSettings.BoxColor,
			FontFamily:   &input.MessageBoxSettings.FontFamily,
			FontSize:     &input.MessageBoxSettings.FontSize,
			Padding:      &input.MessageBoxSettings.Padding,
			ShowTime:     &input.MessageBoxSettings.ShowTime,
			Fill:         &input.MessageBoxSettings.Fill,
		},
		NameBoxSettings: &overlays_dudes.UpdateNameBoxSettingsInput{
			FontFamily:         &input.NameBoxSettings.FontFamily,
			FontSize:           &input.NameBoxSettings.FontSize,
			Fill:               &input.NameBoxSettings.Fill,
			LineJoin:           &input.NameBoxSettings.LineJoin,
			StrokeThickness:    &input.NameBoxSettings.StrokeThickness,
			Stroke:             &input.NameBoxSettings.Stroke,
			FillGradientStops:  &input.NameBoxSettings.FillGradientStops,
			FillGradientType:   &input.NameBoxSettings.FillGradientType,
			FontStyle:          &input.NameBoxSettings.FontStyle,
			FontVariant:        &input.NameBoxSettings.FontVariant,
			FontWeight:         &input.NameBoxSettings.FontWeight,
			DropShadow:         &input.NameBoxSettings.DropShadow,
			DropShadowAlpha:    &input.NameBoxSettings.DropShadowAlpha,
			DropShadowAngle:    &input.NameBoxSettings.DropShadowAngle,
			DropShadowBlur:     &input.NameBoxSettings.DropShadowBlur,
			DropShadowDistance: &input.NameBoxSettings.DropShadowDistance,
			DropShadowColor:    &input.NameBoxSettings.DropShadowColor,
		},
		IgnoreSettings: &overlays_dudes.UpdateIgnoreSettingsInput{
			IgnoreCommands: &input.IgnoreSettings.IgnoreCommands,
			IgnoreUsers:    &input.IgnoreSettings.IgnoreUsers,
			Users:          &input.IgnoreSettings.Users,
		},
		SpitterEmoteSettings: &overlays_dudes.UpdateSpitterEmoteSettingsInput{
			Enabled: &input.SpitterEmoteSettings.Enabled,
		},
	}

	_, err = r.deps.OverlaysDudesService.Update(ctx, id, updateInput)
	if err != nil {
		return false, err
	}

	return true, nil
}

// DudesCreate is the resolver for the dudesCreate field.
func (r *mutationResolver) DudesCreate(ctx context.Context, input gqlmodel.DudesOverlaySettingsInput) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if dashboardID == "" {
		return false, err
	}

	createInput := overlays_dudes.CreateInput{
		ChannelID: dashboardID,
		DudeSettings: overlays_dudes.CreateDudeSettingsInput{
			Color:          input.DudeSettings.Color,
			EyesColor:      input.DudeSettings.EyesColor,
			CosmeticsColor: input.DudeSettings.CosmeticsColor,
			MaxLifeTime:    input.DudeSettings.MaxLifeTime,
			Gravity:        input.DudeSettings.Gravity,
			Scale:          input.DudeSettings.Scale,
			SoundsEnabled:  input.DudeSettings.SoundsEnabled,
			SoundsVolume:   input.DudeSettings.SoundsVolume,
			VisibleName:    input.DudeSettings.VisibleName,
			GrowTime:       input.DudeSettings.GrowTime,
			GrowMaxScale:   input.DudeSettings.GrowMaxScale,
			MaxOnScreen:    input.DudeSettings.MaxOnScreen,
			DefaultSprite:  input.DudeSettings.DefaultSprite,
		},
		MessageBoxSettings: overlays_dudes.CreateMessageBoxSettingsInput{
			Enabled:      input.MessageBoxSettings.Enabled,
			BorderRadius: input.MessageBoxSettings.BorderRadius,
			BoxColor:     input.MessageBoxSettings.BoxColor,
			FontFamily:   input.MessageBoxSettings.FontFamily,
			FontSize:     input.MessageBoxSettings.FontSize,
			Padding:      input.MessageBoxSettings.Padding,
			ShowTime:     input.MessageBoxSettings.ShowTime,
			Fill:         input.MessageBoxSettings.Fill,
		},
		NameBoxSettings: overlays_dudes.CreateNameBoxSettingsInput{
			FontFamily:         input.NameBoxSettings.FontFamily,
			FontSize:           input.NameBoxSettings.FontSize,
			Fill:               input.NameBoxSettings.Fill,
			LineJoin:           input.NameBoxSettings.LineJoin,
			StrokeThickness:    input.NameBoxSettings.StrokeThickness,
			Stroke:             input.NameBoxSettings.Stroke,
			FillGradientStops:  input.NameBoxSettings.FillGradientStops,
			FillGradientType:   input.NameBoxSettings.FillGradientType,
			FontStyle:          input.NameBoxSettings.FontStyle,
			FontVariant:        input.NameBoxSettings.FontVariant,
			FontWeight:         input.NameBoxSettings.FontWeight,
			DropShadow:         input.NameBoxSettings.DropShadow,
			DropShadowAlpha:    input.NameBoxSettings.DropShadowAlpha,
			DropShadowAngle:    input.NameBoxSettings.DropShadowAngle,
			DropShadowBlur:     input.NameBoxSettings.DropShadowBlur,
			DropShadowDistance: input.NameBoxSettings.DropShadowDistance,
			DropShadowColor:    input.NameBoxSettings.DropShadowColor,
		},
		IgnoreSettings: overlays_dudes.CreateIgnoreSettingsInput{
			IgnoreCommands: input.IgnoreSettings.IgnoreCommands,
			IgnoreUsers:    input.IgnoreSettings.IgnoreUsers,
			Users:          input.IgnoreSettings.Users,
		},
		SpitterEmoteSettings: overlays_dudes.CreateSpitterEmoteSettingsInput{
			Enabled: input.SpitterEmoteSettings.Enabled,
		},
	}

	_, err = r.deps.OverlaysDudesService.Create(ctx, createInput)
	if err != nil {
		return false, err
	}

	return true, nil
}

// DudesDelete is the resolver for the dudesDelete field.
func (r *mutationResolver) DudesDelete(ctx context.Context, id uuid.UUID) (bool, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if dashboardID == "" {
		return false, err
	}

	err = r.deps.OverlaysDudesService.Delete(ctx, id)
	if err != nil {
		return false, err
	}

	return true, nil
}

// DudesGetByID is the resolver for the dudesGetById field.
func (r *queryResolver) DudesGetByID(ctx context.Context, id uuid.UUID) (*gqlmodel.DudesOverlaySettings, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if dashboardID == "" {
		return nil, err
	}

	e, err := r.deps.OverlaysDudesService.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	result := mappers.DudesOverlaySettingsEntityToGql(e)
	return &result, nil
}

// DudesGetAll is the resolver for the dudesGetAll field.
func (r *queryResolver) DudesGetAll(ctx context.Context) ([]gqlmodel.DudesOverlaySettings, error) {
	dashboardID, err := r.deps.Sessions.GetSelectedDashboard(ctx)
	if dashboardID == "" {
		return nil, err
	}

	entities, err := r.deps.OverlaysDudesService.GetManyByChannelID(ctx, dashboardID)
	if err != nil {
		return nil, err
	}

	return lo.Map(
		entities, func(entity entity.DudesOverlaySettings, _ int) gqlmodel.DudesOverlaySettings {
			return mappers.DudesOverlaySettingsEntityToGql(entity)
		},
	), nil
}

// DudesSettings is the resolver for the dudesSettings field.
func (r *subscriptionResolver) DudesSettings(ctx context.Context, id uuid.UUID, apiKey string) (<-chan *gqlmodel.DudesSettingsSubscriptionData, error) {
	user, err := r.deps.UsersService.GetByApiKey(ctx, apiKey)
	if err != nil {
		return nil, err
	}

	channel := make(chan *gqlmodel.DudesSettingsSubscriptionData)

	twitchChannel, err := r.deps.CachedTwitchClient.GetUserById(ctx, user.ID)
	if err != nil {
		return nil, err
	}

	initialSettings, err := r.deps.OverlaysDudesService.GetByID(ctx, id)
	if err != nil {
		return nil, err
	}

	go func() {
		sub, err := r.deps.WsRouter.Subscribe(
			[]string{
				overlays_dudes.CreateDudesWsRouterKey(user.ID, id),
			},
		)
		if err != nil {
			panic(err)
		}
		defer func() {
			sub.Unsubscribe()
			close(channel)
		}()

		mappedInitialSettings := mappers.DudesOverlaySettingsEntityToGql(initialSettings)
		initialData := gqlmodel.DudesSettingsSubscriptionData{
			ChannelID:          twitchChannel.ID,
			ChannelName:        twitchChannel.Login,
			ChannelDisplayName: twitchChannel.DisplayName,
			Settings:           &mappedInitialSettings,
		}
		channel <- &initialData

		for {
			select {
			case <-ctx.Done():
				return
			case data := <-sub.GetChannel():
				var settings entity.DudesOverlaySettings
				if err := json.Unmarshal(data, &settings); err != nil {
					r.deps.Logger.Error("cannot unmarshall dudes settings", slog.Any("err", err))
					continue
				}

				convertedSettings := mappers.DudesOverlaySettingsEntityToGql(settings)
				mappedData := gqlmodel.DudesSettingsSubscriptionData{
					ChannelID:          twitchChannel.ID,
					ChannelName:        twitchChannel.Login,
					ChannelDisplayName: twitchChannel.DisplayName,
					Settings:           &convertedSettings,
				}
				channel <- &mappedData
			}
		}
	}()

	return channel, nil
}
