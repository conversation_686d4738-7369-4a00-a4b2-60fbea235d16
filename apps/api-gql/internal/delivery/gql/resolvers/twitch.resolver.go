package resolvers

// This file will be automatically regenerated based on the schema, any resolver implementations
// will be copied through when generating and any unknown code will be moved to the end.
// Code generated by github.com/99designs/gqlgen version v0.17.57

import (
	"context"
	"fmt"

	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/dataloader"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/gqlmodel"
	"github.com/twirapp/twir/apps/api-gql/internal/delivery/gql/mappers"
)

// TwitchGetUserByID is the resolver for the twitchGetUserById field.
func (r *queryResolver) TwitchGetUserByID(ctx context.Context, id string) (*gqlmodel.TwirUserTwitchInfo, error) {
	if id == "" {
		return nil, fmt.Errorf("id is required")
	}

	return dataloader.GetHelixUserById(ctx, id)
}

// TwitchGetUserByName is the resolver for the twitchGetUserByName field.
func (r *queryResolver) TwitchGetUserByName(ctx context.Context, name string) (*gqlmodel.TwirUserTwitchInfo, error) {
	if name == "" {
		return nil, fmt.Errorf("name is required")
	}

	return dataloader.GetHelixUserByName(ctx, name)
}

// TwitchGetChannelRewards is the resolver for the twitchGetChannelRewards field.
func (r *queryResolver) TwitchGetChannelRewards(ctx context.Context, channelID *string) (*gqlmodel.TwirTwitchChannelRewardResponse, error) {
	var channelId string
	if channelID == nil {
		dashboardId, err := r.deps.Sessions.GetSelectedDashboard(ctx)
		if err != nil {
			return nil, err
		}
		channelId = dashboardId
	} else {
		channelId = *channelID
	}

	if channelId == "" {
		return nil, fmt.Errorf("channelID is required")
	}

	rewards, err := r.deps.TwitchService.GetRewardsByChannelID(ctx, channelId)
	if err != nil {
		return nil, err
	}

	mappedRewards := make([]gqlmodel.TwirTwitchChannelReward, 0, len(rewards.Rewards))
	for _, reward := range rewards.Rewards {
		mappedRewards = append(mappedRewards, mappers.TwitchCustomRewardTo(reward))
	}

	return &gqlmodel.TwirTwitchChannelRewardResponse{
		PartnerOrAffiliate: rewards.IsPartnerOrAffiliate,
		Rewards:            mappedRewards,
	}, nil
}

// TwitchGetChannelBadges is the resolver for the twitchGetChannelBadges field.
func (r *queryResolver) TwitchGetChannelBadges(ctx context.Context, channelID *string) (*gqlmodel.TwirTwitchChannelBadgeResponse, error) {
	var userId string
	if channelID != nil {
		userId = *channelID
	} else {
		dashboardId, err := r.deps.Sessions.GetSelectedDashboard(ctx)
		if err != nil {
			return nil, err
		}
		userId = dashboardId
	}

	if userId == "" {
		return nil, fmt.Errorf("channelID is required")
	}

	badges, err := r.deps.TwitchService.GetChannelChatBadges(ctx, userId)
	if err != nil {
		return nil, err
	}

	mappedBadges := make([]gqlmodel.TwitchBadge, 0, len(badges))
	for _, badge := range badges {
		mappedBadges = append(mappedBadges, mappers.TwitchBadgeTo(badge))
	}

	return &gqlmodel.TwirTwitchChannelBadgeResponse{
		Badges: mappedBadges,
	}, nil
}

// TwitchGetGlobalBadges is the resolver for the twitchGetGlobalBadges field.
func (r *queryResolver) TwitchGetGlobalBadges(ctx context.Context) (*gqlmodel.TwirTwitchGlobalBadgeResponse, error) {
	badges, err := r.deps.TwitchService.GetGlobalChatBadges(ctx)
	if err != nil {
		return nil, err
	}

	mappedBadges := make([]gqlmodel.TwitchBadge, 0, len(badges))
	for _, badge := range badges {
		mappedBadges = append(mappedBadges, mappers.TwitchBadgeTo(badge))
	}

	return &gqlmodel.TwirTwitchGlobalBadgeResponse{
		Badges: mappedBadges,
	}, nil
}
