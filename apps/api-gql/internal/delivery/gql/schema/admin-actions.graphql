extend type Mutation {
	dropAllAuthSessions: <PERSON>olean! @isAuthenticated @isAdmin
	eventsubSubscribe(opts: EventsubSubscribeInput!): <PERSON>olean! @isAuthenticated @isAdmin
	rescheduleTimers: <PERSON><PERSON><PERSON>! @isAuthenticated @isAdmin
	eventsubInitChannels: <PERSON><PERSON><PERSON>! @isAuthenticated @isAdmin
}

input EventsubSubscribeInput {
	type: String! @validate(constraint: "max=50")
	version: String! @validate(constraint: "max=50")
	condition: EventsubSubscribeConditionInput!
}

enum EventsubSubscribeConditionInput {
	CHANNEL
	USER
	CHANNEL_WITH_MODERATOR_ID
	CHANNEL_WITH_BOT_ID
}
