extend type Query {
	"""
	Twir badges
	"""
	twirBadges: [Badge!]!
}

extend type Mutation {
	badgesDelete(id: UUID!): Boolean! @isAuthenticated @isAdmin
	badgesUpdate(id: UUID!, opts: TwirBadgeUpdateOpts!): Badge! @isAuthenticated @isAdmin
	badgesCreate(opts: TwirBadgeCreateOpts!): Badge! @isAuthenticated @isAdmin
	badgesAddUser(id: UUID!, userId: String!): Boolean! @isAuthenticated @isAdmin
	badgesRemoveUser(id: UUID!, userId: String!): Boolean! @isAuthenticated @isAdmin
}

type Badge {
	id: UUID!
	name: String!
	createdAt: String!
	fileUrl: String!
	enabled: Boolean!
	"""
	IDS of users which has this badge
	"""
	users: [String!] @goField(forceResolver: true)
	ffzSlot: Int!
}

input TwirBadgeUpdateOpts {
	name: String @validate(constraint: "max=200")
	file: Upload
	enabled: Boolean
	ffzSlot: Int @validate(constraint: "max=999999")
}

input TwirBadgeCreateOpts {
	name: String! @validate(constraint: "max=500")
	file: Upload!
	enabled: Boolean
	ffzSlot: Int! @validate(constraint: "max=999999")
}
