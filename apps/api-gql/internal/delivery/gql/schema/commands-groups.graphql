extend type Query {
	commandsGroups: [CommandGroup!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_COMMANDS)
}

extend type Mutation {
	commandsGroupsCreate(opts: CommandsGroupsCreateOpts!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
	commandsGroupsUpdate(id: ID!, opts: CommandsGroupsUpdateOpts!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
	commandsGroupsRemove(id: ID!): Bo<PERSON>an! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
}

type CommandGroup {
	id: ID!
	name: String!
	color: String!
}

input CommandsGroupsCreateOpts {
	name: String! @validate(constraint: "max=50")
	color: String! @validate(constraint: "max=50")
}

input CommandsGroupsUpdateOpts {
	name: String @validate(constraint: "max=50")
	color: String @validate(constraint: "max=50")
}
