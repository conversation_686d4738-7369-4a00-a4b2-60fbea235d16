extend type Query {
	commands: [Command!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_COMMANDS)
	commandsPublic(channelId: ID!): [PublicCommand!]!
}

extend type Mutation {
	commandsCreate(opts: CommandsCreateOpts!): CommandCreatePayload! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
	commandsUpdate(id: UUID!, opts: CommandsUpdateOpts!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
	commandsRemove(id: UUID!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
	commandsCreateMultiple(commands: [CommandsCreateOpts!]!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_COMMANDS)
}

type Command {
	id: UUID!
	name: String!
	description: String!
	aliases: [String!]!
	responses: [CommandResponse!]! @goField(forceResolver: true)
	cooldown: Int!
	cooldownType: String!
	enabled: Boolean!
	visible: Boolean!
	default: Boolean!
	defaultName: String
	module: String!
	isReply: Boolean!
	keepResponsesOrder: Boolean!
	deniedUsersIds: [String!]!
	allowedUsersIds: [String!]!
	rolesIds: [String!]!
	onlineOnly: Boolean!
	offlineOnly: Boolean!
	cooldownRolesIds: [String!]!
	enabledCategories: [String!]!
	requiredWatchTime: Int!
	requiredMessages: Int!
	requiredUsedChannelPoints: Int!
	groupId: String
	group: CommandGroup @goField(forceResolver: true)
	expiresAt: Int
	expiresType: CommandExpiresType
}

type CommandResponse {
	id: UUID!
	commandId: ID!
	text: String!
	twitchCategoriesIds: [String!]!
	twitchCategories: [TwitchCategory!]! @goField(forceResolver: true)
	onlineOnly: Boolean!
	offlineOnly: Boolean!
}

type PublicCommand {
	name: String!
	description: String!
	aliases: [String!]!
	responses: [String!]!
	cooldown: Int!
	cooldownType: String!
	module: String!
	permissions: [PublicCommandPermission!]!
	groupId: String
	group: CommandGroup @goField(forceResolver: true)
}

type PublicCommandPermission {
	name: String!
	type: String!
}

input CommandsCreateOpts {
	name: String! @validate(constraint: "max=50")
	description: String! @validate(constraint: "max=500")
	aliases: [String!]! @validate(constraint: "dive,max=50,min=1")
	responses: [CreateOrUpdateCommandResponseInput!]! @validate(constraint: "max=3")
	cooldown: Int! @validate(constraint: "max=90000")
	cooldownType: String!  @validate(constraint: "max=500")
	enabled: Boolean!
	visible: Boolean!
	isReply: Boolean!
	keepResponsesOrder: Boolean!
	deniedUsersIds: [String!]! @validate(constraint: "dive,max=500")
	allowedUsersIds: [String!]! @validate(constraint: "dive,max=500")
	rolesIds: [String!]! @validate(constraint: "dive,max=500")
	onlineOnly: Boolean!
	offlineOnly: Boolean!
	cooldownRolesIds: [String!]! @validate(constraint: "dive,max=500")
	enabledCategories: [String!]! @validate(constraint: "dive,max=500")
	requiredWatchTime: Int! @validate(constraint: "lte=900000000000")
	requiredMessages: Int! @validate(constraint: "lte=900000000000")
	requiredUsedChannelPoints: Int! @validate(constraint: "lte=900000000000")
	groupId: String @validate(constraint: "max=500,omitempty")
	expiresAt: Int @validate(constraint: "lte=90000000000000,omitempty")
	expiresType: CommandExpiresType
}

input CommandsUpdateOpts {
	name: String @validate(constraint: "max=50,omitempty")
	description: String @validate(constraint: "max=500,omitempty")
	aliases: [String!] @validate(constraint: "dive,max=50,min=1,omitempty")
	responses: [CreateOrUpdateCommandResponseInput!] @validate(constraint: "max=3,omitempty")
	cooldown: Int @validate(constraint: "lte=90000,omitempty")
	cooldownType: String @validate(constraint: "lte=500,omitempty")
	enabled: Boolean
	visible: Boolean
	isReply: Boolean
	keepResponsesOrder: Boolean
	deniedUsersIds: [String!] @validate(constraint: "dive,max=500,omitempty")
	allowedUsersIds: [String!] @validate(constraint: "dive,max=500,omitempty")
	rolesIds: [String!] @validate(constraint: "dive,max=500,omitempty")
	onlineOnly: Boolean
	cooldownRolesIds: [String!] @validate(constraint: "dive,max=500,omitempty")
	enabledCategories: [String!] @validate(constraint: "dive,max=500,omitempty")
	requiredWatchTime: Int @validate(constraint: "lte=900000000000,omitempty")
	requiredMessages: Int @validate(constraint: "lte=900000000000,omitempty")
	requiredUsedChannelPoints: Int @validate(constraint: "lte=900000000000,omitempty")
	groupId: String @validate(constraint: "max=500,omitempty")
	expiresAt: Int @validate(constraint: "lte=90000000000000,omitempty")
	expiresType: CommandExpiresType
	offlineOnly: Boolean
}

input CreateOrUpdateCommandResponseInput {
	text: String! @validate(constraint: "max=500")
	twitchCategoriesIds: [String!]! @validate(constraint: "max=500")
	onlineOnly: Boolean!
	offlineOnly: Boolean!
}

enum CommandExpiresType {
	DISABLE
	DELETE
}

type CommandCreatePayload {
	id: String!
}
