extend type Query {
	rewardsRedemptionsHistory(opts: TwitchRedemptionsOpts!): TwitchRedemptionResponse! @isAuthenticated
}

extend type Subscription {
	rewardsActivation: TwitchRedemption! @isAuthenticated @hasAccessToSelectedDashboard
}

type TwitchRedemptionResponse {
	redemptions: [TwitchRedemption!]!
	total: Int!
}

type TwitchRedemption {
	id: ID!
	channelId: String!
	user: TwirUserTwitchInfo! @goField(forceResolver: true)
	reward: TwitchReward!
	redeemedAt: Time!
	prompt: String
}

input TwitchRedemptionsOpts {
	byChannelId: ID
	userSearch: String
	page: Int
	perPage: Int @validate(constraint: "lte=100")
	rewardsIds: [ID!]
}
