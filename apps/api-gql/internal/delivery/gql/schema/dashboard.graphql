extend type Subscription {
	dashboardStats: DashboardStats! @isAuthenticated
	botStatus: BotStatus! @isAuthenticated
}

extend type Mutation {
	botJoinLeave(action: BotJoinLeaveAction!): Boolean! @isAuthenticated
}

type DashboardStats {
	categoryId: ID!
	categoryName: String!
	viewers: Int
	startedAt: Time
	title: String!
	chatMessages: Int!
	followers: Int!
	usedEmotes: Int!
	requestedSongs: Int!
	subs: Int!
}

type BotStatus {
	isMod: Boolean!
	botId: String!
	botName: String!
	enabled: Boolean!
}

enum BotJoinLeaveAction {
	JOIN
	LEAVE
}
