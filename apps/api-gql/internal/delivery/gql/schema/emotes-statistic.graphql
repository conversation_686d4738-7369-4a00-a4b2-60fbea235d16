extend type Query {
	emotesStatistics(opts: EmotesStatisticsOpts!): EmotesStatisticResponse! @isAuthenticated
	emotesStatisticEmoteDetailedInformation(opts: EmotesStatisticEmoteDetailedOpts!): EmotesStatisticEmoteDetailedResponse! @isAuthenticated
}

type EmotesStatisticResponse {
	emotes: [EmotesStatistic!]!
	total: Int!
}

type EmotesStatistic {
	emoteName: String!
	totalUsages: Int!
	lastUsedTimestamp: Int!
	graphicUsages: [EmoteStatisticUsage!]!
}

enum EmotesStatisticsOptsOrder {
	ASC
	DESC
}

input EmotesStatisticsOpts {
	search: String @validate(constraint: "max=500")
	page: Int
	perPage: Int @validate(constraint: "lte=100")
	graphicRange: EmoteStatisticRange
	order: EmotesStatisticsOptsOrder
}

enum EmoteStatisticRange {
	LAST_DAY
	LAST_WEEK
	LAST_MONTH
	LAST_THREE_MONTH
	LAST_YEAR
}

input EmotesStatisticEmoteDetailedOpts {
	emoteName: String!
	range: EmoteStatisticRange!
	usagesByUsersPage: Int @validate(constraint: "lte=100")
	usagesByUsersPerPage: Int @validate(constraint: "lte=100")
	topUsersPage: Int
	topUsersPerPage: Int @validate(constraint: "lte=100")
}

type EmotesStatisticEmoteDetailedResponse {
	emoteName: String!
	totalUsages: Int!
	lastUsedTimestamp: Int!
	graphicUsages: [EmoteStatisticUsage!]!
	usagesHistory: [EmoteStatisticUserUsage!]!
	usagesByUsersTotal: Int!
	topUsers: [EmoteStatisticTopUser!]!
	topUsersTotal: Int!
}

type EmoteStatisticUsage {
	count: Int!
	timestamp: Int!
}

type EmoteStatisticUserUsage {
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	date: Time!
}

type EmoteStatisticTopUser {
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	count: Int!
}
