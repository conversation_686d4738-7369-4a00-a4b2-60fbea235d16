extend type Query {
	greetings: [Greeting!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_GREETINGS)
}

extend type Mutation {
	greetingsCreate(opts: GreetingsCreateInput!): Greeting! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_GREETINGS)
	greetingsUpdate(id: UUID!, opts: GreetingsUpdateInput!): Greeting! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_GREETINGS)
	greetingsRemove(id: UUID!): Bo<PERSON><PERSON>! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_GREETINGS)
}

type Greeting {
	id: UUID!
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	enabled: Boolean!
	isReply: Boolean!
	text: String!
	withShoutOut: Boolean!
}

input GreetingsCreateInput {
	enabled: Boolean!
	isReply: Boolean!
	userId: String! @validate(constraint: "max=90")
	text: String! @validate(constraint: "max=500")
	withShoutOut: Boolean!
}

input GreetingsUpdateInput {
	enabled: Boolean
	isReply: Boolean
	userId: String @validate(constraint: "max=90,omitempty")
	text: String @validate(constraint: "max=500,omitempty")
	withShoutOut: Boolean
}
