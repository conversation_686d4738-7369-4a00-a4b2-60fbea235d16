extend type Query {
	keywords: [Keyword!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_KEYWORDS)
}

extend type Mutation {
	keywordCreate(opts: KeywordCreateInput!): Keyword! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_KEYWORDS)
	keywordUpdate(id: UUID!, opts: KeywordUpdateInput!): Keyword! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_KEYWORDS)
	keywordRemove(id: UUID!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_KEYWORDS)
}

type Keyword {
	id: UUID!
	text: String!
	response: String
	enabled: Boolean!
	cooldown: Int!
	isReply: Boolean!
	isRegularExpression: Boolean!
	usageCount: Int!
}

input KeywordCreateInput {
	text: String! @validate(constraint: "max=5000")
	response: String @validate(constraint: "max=500,omitempty")
	cooldown: Int @validate(constraint: "max=999999")
	enabled: Boolean
	usageCount: Int @validate(constraint: "max=999999999999999")
	isRegularExpression: Boolean
	isReply: Boolean
}

input KeywordUpdateInput {
	text: String @validate(constraint: "max=5000")
	response: String @validate(constraint: "max=500,omitempty")
	cooldown: Int @validate(constraint: "max=999999")
	enabled: Boolean
	usageCount: Int @validate(constraint: "max=999999999999999")
	isRegularExpression: Boolean
	isReply: Boolean
}
