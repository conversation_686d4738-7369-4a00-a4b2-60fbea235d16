extend type Query {
	moderationSettings: [ModerationSettingsItem!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_MODERATION)
	moderationLanguagesAvailableLanguages: ModerationLanguagesAvailableLanguagesOutput! @isAuthenticated
}

extend type Mutation {
	moderationSettingsCreate(input: ModerationSettingsCreateOrUpdateInput!): ModerationSettingsItem! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_MODERATION)
	moderationSettingsUpdate(id: UUID!, input: ModerationSettingsCreateOrUpdateInput!): ModerationSettingsItem! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_MODERATION)
	moderationSettingsDelete(id: UUID!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_MODERATION)
}

type ModerationLanguagesAvailableLanguagesOutput {
	languages: [ModerationLanguagesAvailableLanguage!]!
}

type ModerationLanguagesAvailableLanguage {
	iso_639_1: String!
	name: String!
}

enum ModerationSettingsType {
	links
	deny_list
	symbols
	long_message
	caps
	emotes
	language
	one_man_spam
}

type ModerationSettingsItem {
	id: UUID!
	type: ModerationSettingsType!
	name: String
	enabled: Boolean!
	banTime: Int!
	banMessage: String!
	warningMessage: String!
	checkClips: Boolean!
	triggerLength: Int!
	maxPercentage: Int!
	denyList: [String!]!
	deniedChatLanguages: [String!]!
	denyListRegexpEnabled: Boolean!
	denyListWordBoundaryEnabled: Boolean!
	denyListSensitivityEnabled: Boolean!
	excludedRoles: [String!]!
	maxWarnings: Int!
	oneManSpamMinimumStoredMessages: Int!
	oneManSpamMessageMemorySeconds: Int!
	createdAt: Time!
	updatedAt: Time!
	languageExcludedWords: [String!]!
}

input ModerationSettingsCreateOrUpdateInput {
	type: ModerationSettingsType!
	name: String
	enabled: Boolean!
	banTime: Int!
	banMessage: String!
	warningMessage: String!
	checkClips: Boolean!
	triggerLength: Int!
	maxPercentage: Int!
	denyList: [String!]!
	deniedChatLanguages: [String!]!
	denyListRegexpEnabled: Boolean!
	denyListWordBoundaryEnabled: Boolean!
	denyListSensitivityEnabled: Boolean!
	excludedRoles: [String!]!
	maxWarnings: Int!
	oneManSpamMinimumStoredMessages: Int!
	oneManSpamMessageMemorySeconds: Int!
	languageExcludedWords: [String!]!
}
