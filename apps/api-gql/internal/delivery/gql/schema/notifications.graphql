extend type Query {
	notificationsByUser: [UserNotification!]! @isAuthenticated
	notificationsByAdmin(opts: AdminNotificationsParams!): AdminNotificationsResponse! @isAuthenticated @isAdmin
}

extend type Mutation {
	notificationsCreate(
		text: String @validate(constraint: "max=10000"),
		editorJsJson: String @validate(constraint: "max=10000")
		userId: String
	): AdminNotification! @isAuthenticated @isAdmin
	notificationsUpdate(id: ID!, opts: NotificationUpdateOpts!): AdminNotification! @isAuthenticated @isAdmin
	notificationsDelete(id: ID!): Boolean! @isAuthenticated @isAdmin
}

extend type Subscription {
	"""
	`newNotification` will return a stream of `Notification` objects.
	"""
	newNotification: UserNotification! @isAuthenticated
}

interface Notification {
	id: ID!
	userId: ID
	text: String
	editorJsJson: String
	createdAt: Time!
}

type UserNotification implements Notification {
	id: ID!
	userId: ID
	text: String
	createdAt: Time!
	editorJsJson: String
}

type AdminNotification implements Notification {
	id: ID!
	text: String
	userId: ID
	twitchProfile: TwirUserTwitchInfo @goField(forceResolver: true)
	createdAt: Time!
	editorJsJson: String
}

enum NotificationType {
	GLOBAL
	USER
}

input AdminNotificationsParams {
	search: String
	page: Int
	perPage: Int @validate(constraint: "lte=100")
	type: NotificationType
}

input NotificationUpdateOpts {
	text: String @validate(constraint: "max=10000")
	editorJsJson: String @validate(constraint: "max=10000")
}

type AdminNotificationsResponse {
	notifications: [AdminNotification!]!
	total: Int!
}
