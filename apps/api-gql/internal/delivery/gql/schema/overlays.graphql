extend type Query {
	chatOverlays: [ChatOverlay!]! @isAuthenticated @hasChannelRolesDashboardPermission(permission: VIEW_OVERLAYS)
	chatOverlaysById(id: String!): ChatOverlay @isAuthenticated @hasChannelRolesDashboardPermission(permission: VIEW_OVERLAYS)

	nowPlayingOverlays: [NowPlayingOverlay!]! @isAuthenticated @hasChannelRolesDashboardPermission(permission: VIEW_OVERLAYS)
	nowPlayingOverlaysById(id: String!): NowPlayingOverlay @isAuthenticated @hasChannelRolesDashboardPermission(permission: VIEW_OVERLAYS)

	ttsPublicUsersSettings(channelId: String!): [TTSUserSettings!]!
}

extend type Mutation {
	chatOverlayUpdate(id: String!, opts: ChatOverlayMutateOpts!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANA<PERSON>_OVERLAYS)
	chatOverlayCreate(opts: ChatOverlayMutateOpts!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_OVERLAYS)
	chatOverlayDelete(id: String!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_OVERLAYS)

	nowPlayingOverlayUpdate(id: String!, opts: NowPlayingOverlayMutateOpts!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_OVERLAYS)
	nowPlayingOverlayCreate(opts: NowPlayingOverlayMutateOpts!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_OVERLAYS)
	nowPlayingOverlayDelete(id: String!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_OVERLAYS)
}

extend type Subscription {
	chatOverlaySettings(id: String!, apiKey: String!): ChatOverlay

	nowPlayingOverlaySettings(id: String!, apiKey: String!): NowPlayingOverlay
	nowPlayingCurrentTrack(apiKey: String!): NowPlayingOverlayTrack
}

enum ChatOverlayAnimation {
	DISABLED
	DEFAULT
}

type ChatOverlay {
	id: String!
	messageHideTimeout: Int!
	messageShowDelay: Int!
	preset: String!
	fontSize: Int!
	hideCommands: Boolean!
	hideBots: Boolean!
	fontFamily: String!
	showBadges: Boolean!
	showAnnounceBadge: Boolean!
	textShadowColor: String!
	textShadowSize: Int!
	chatBackgroundColor: String!
	direction: String!
	fontWeight: Int!
	fontStyle: String!
	paddingContainer: Int!
	animation: ChatOverlayAnimation!
}

input ChatOverlayMutateOpts {
	messageHideTimeout: Int
	messageShowDelay: Int
	preset: String @validate(constraint: "max=90,omitempty")
	fontSize: Int @validate(constraint: "lte=1000,omitempty")
	hideCommands: Boolean
	hideBots: Boolean
	fontFamily: String @validate(constraint: "max=90,omitempty")
	showBadges: Boolean
	showAnnounceBadge: Boolean
	textShadowColor: String @validate(constraint: "max=90,omitempty")
	textShadowSize: Int @validate(constraint: "lte=1000,omitempty")
	chatBackgroundColor: String @validate(constraint: "max=1000,omitempty")
	direction: String @validate(constraint: "max=100,omitempty")
	fontWeight: Int @validate(constraint: "lte=5000,omitempty")
	fontStyle: String @validate(constraint: "max=5000,omitempty")
	paddingContainer: Int @validate(constraint: "lte=1000,omitempty")
	animation: ChatOverlayAnimation
}

enum NowPlayingOverlayPreset {
	TRANSPARENT
	AIDEN_REDESIGN
	SIMPLE_LINE
}

type NowPlayingOverlay {
	id: String!
	preset: NowPlayingOverlayPreset!
	channelId: String!
	fontFamily: String!
	fontWeight: Int!
	backgroundColor: String!
	showImage: Boolean!
	hideTimeout: Int
}

input NowPlayingOverlayMutateOpts {
	preset: NowPlayingOverlayPreset @validate(constraint: "max=90,omitempty")
	fontFamily: String @validate(constraint: "max=90,omitempty")
	fontWeight: Int @validate(constraint: "lte=1000,omitempty")
	backgroundColor: String @validate(constraint: "max=1000,omitempty")
	showImage: Boolean
	hideTimeout: Int @validate(constraint: "lte=10000,omitempty")
}

type NowPlayingOverlayTrack {
	artist: String!
	title: String!
	imageUrl: String
}

type TTSUserSettings {
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	rate: Int!
	pitch: Int!
	volume: Int!
	voice: String!
	isChannelOwner: Boolean!
}
