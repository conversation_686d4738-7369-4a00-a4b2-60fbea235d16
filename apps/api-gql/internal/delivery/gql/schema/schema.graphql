type Query
type Mutation
type Subscription

directive @goField(forceResolver: <PERSON><PERSON><PERSON>, name: String, omittable: <PERSON><PERSON><PERSON>) on INPUT_FIELD_DEFINITION
	| FIELD_DEFINITION

directive @isAuthenticated on FIELD_DEFINITION

directive @isAdmin on FIELD_DEFINITION

directive @hasAccessToSelectedDashboard on FIELD_DEFINITION

directive @validate(constraint: String!) on INPUT_FIELD_DEFINITION | ARGUMENT_DEFINITION


scalar Upload
scalar Time
scalar UUID
