extend type Query {
	songRequests: SongRequestsSettings @isAuthenticated @hasChannelRolesDashboardPermission(permission: VIEW_SONG_REQUESTS)
	songRequestsSearchChannelOrVideo(opts: SongRequestsSearchChannelOrVideoOpts!): SongRequestsSearchChannelOrVideoResponse! @isAuthenticated
	songRequestsPublicQueue(channelId: String!): [SongRequestPublic!]!
}

extend type Mutation {
	songRequestsUpdate(opts: SongRequestsSettingsOpts!): Boolean! @isAuthenticated @hasChannelRolesDashboardPermission(permission: MANAGE_SONG_REQUESTS)
}

type SongRequestsSettings {
	enabled: Boolean!
	acceptOnlyWhenOnline: Boolean!
	maxRequests: Int!
	channelPointsRewardId: String
	announcePlay: Boolean!
	neededVotesForSkip: Int!
	user: SongRequestsUserSettings!
	song: SongRequestsSongSettings!
	denyList: SongRequestsDenyList!
	translations: SongRequestsTranslations!
	takeSongFromDonationMessages: Boolean!
	playerNoCookieMode: Boolean!
}

type SongRequestsUserSettings {
	maxRequests: Int!
	minWatchTime: Int!
	minMessages: Int!
	minFollowTime: Int!
}

type SongRequestsSongSettings {
	minLength: Int!
	maxLength: Int!
	minViews: Int!
	acceptedCategories: [String!]!
}

type SongRequestsDenyList {
	users: [String!]!
	songs: [String!]!
	channels: [String!]!
	artistsNames: [String!]!
	words: [String!]!
}

type SongRequestsTranslations {
	nowPlaying: String!
	notEnabled: String!
	noText: String!
	acceptOnlyWhenOnline: String!
	user: SongRequestsUserTranslations!
	song: SongRequestsSongTranslations!
	channel: SongRequestsChannelTranslations!
}

type SongRequestsUserTranslations {
	denied: String!
	maxRequests: String!
	minMessages: String!
	minWatched: String!
	minFollow: String!
}

type SongRequestsSongTranslations {
	denied: String!
	notFound: String!
	alreadyInQueue: String!
	ageRestrictions: String!
	cannotGetInformation: String!
	live: String!
	maxLength: String!
	minLength: String!
	requestedMessage: String!
	maximumOrdered: String!
	minViews: String!
}

type SongRequestsChannelTranslations {
	denied: String!
}

input SongRequestsSettingsOpts {
	enabled: Boolean!
	acceptOnlyWhenOnline: Boolean!
	maxRequests: Int! @validate(constraint: "max=500")
	channelPointsRewardId: String
	announcePlay: Boolean!
	neededVotesForSkip: Int! @validate(constraint: "min=0,max=100")
	user: SongRequestsUserSettingsOpts!
	song: SongRequestsSongSettingsOpts!
	denyList: SongRequestsDenyListOpts!
	translations: SongRequestsTranslationsOpts!
	takeSongFromDonationMessages: Boolean!
	playerNoCookieMode: Boolean!
}

input SongRequestsUserSettingsOpts {
	maxRequests: Int! @validate(constraint: "max=500")
	minWatchTime: Int! @validate(constraint: "max=9999999999")
	minMessages: Int! @validate(constraint: "max=9999999999")
	minFollowTime: Int! @validate(constraint: "max=9999999999")
}

input SongRequestsSongSettingsOpts {
	minLength: Int! @validate(constraint: "max=86399")
	maxLength: Int! @validate(constraint: "max=86400")
	minViews: Int! @validate(constraint: "max=10000000000000")
	acceptedCategories: [String!]! @validate(constraint: "dive,max=500")
}

input SongRequestsDenyListOpts {
	users: [String!]! @validate(constraint: "dive,max=500")
	songs: [String!]! @validate(constraint: "dive,max=500")
	channels: [String!]! @validate(constraint: "dive,max=500")
	artistsNames: [String!]! @validate(constraint: "dive,max=500")
	words: [String!]! @validate(constraint: "dive,max=500")
}

input SongRequestsTranslationsOpts {
	nowPlaying: String! @validate(constraint: "max=400")
	notEnabled: String! @validate(constraint: "max=400")
	noText: String! @validate(constraint: "max=400")
	acceptOnlyWhenOnline: String! @validate(constraint: "max=400")
	user: SongRequestsUserTranslationsOpts! @validate(constraint: "max=400")
	song: SongRequestsSongTranslationsOpts! @validate(constraint: "max=400")
	channel: SongRequestsChannelTranslationsOpts! @validate(constraint: "max=400")
}

input SongRequestsUserTranslationsOpts {
	denied: String! @validate(constraint: "max=400")
	maxRequests: String! @validate(constraint: "max=400")
	minMessages: String! @validate(constraint: "max=400")
	minWatched: String! @validate(constraint: "max=400")
	minFollow: String! @validate(constraint: "max=400")
}

input SongRequestsSongTranslationsOpts {
	denied: String! @validate(constraint: "max=400")
	notFound: String! @validate(constraint: "max=400")
	alreadyInQueue: String! @validate(constraint: "max=400")
	ageRestrictions: String! @validate(constraint: "max=400")
	cannotGetInformation: String! @validate(constraint: "max=400")
	live: String! @validate(constraint: "max=400")
	maxLength: String! @validate(constraint: "max=400")
	minLength: String! @validate(constraint: "max=400")
	requestedMessage: String! @validate(constraint: "max=400")
	maximumOrdered: String! @validate(constraint: "max=400")
	minViews: String! @validate(constraint: "max=400")
}

input SongRequestsChannelTranslationsOpts {
	denied: String! @validate(constraint: "max=400")
}

enum SongRequestsSearchChannelOrVideoOptsType {
	CHANNEL
	VIDEO
}

input SongRequestsSearchChannelOrVideoOpts {
	type: SongRequestsSearchChannelOrVideoOptsType!
	query: [String!]!
}

type SongRequestsSearchChannelOrVideoResponse {
	items: [SongRequestsSearchChannelOrVideoItem!]!
}

type SongRequestsSearchChannelOrVideoItem {
	id: String!
	title: String!
	thumbnail: String!
}

type SongRequestPublic {
	title: String!
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	createdAt: Time!
	songLink: String!
	durationSeconds: Int!
}
