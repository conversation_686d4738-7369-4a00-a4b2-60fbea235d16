extend type Query {
	timers: [Timer!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_TIMERS)
}

extend type Mutation {
	timersCreate(opts: TimerCreateInput!): Timer! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_TIMERS)
	timersCreateMany(opts: [TimerCreateInput!]!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_TIMERS)
	timersUpdate(id: UUID!, opts: TimerUpdateInput!): Timer! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_TIMERS)
	timersRemove(id: UUID!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_TIMERS)
}

type Timer {
	id: UUID!
	name: String!
	enabled: Boolean!
	timeInterval: Int!
	messageInterval: Int!
	responses: [TimerResponse!]!
}

type TimerResponse {
	id: UUID!
	text: String!
	isAnnounce: Boolean!
	count: Int!
}

input TimerCreateInput {
	name: String! @validate(constraint: "max=50")
	enabled: Boolean!
	timeInterval: Int! @validate(constraint: "min=1,max=999999")
	messageInterval: Int! @validate(constraint: "min=0,max=999999")
	responses: [TimerResponseCreateInput!]!
}

input TimerResponseCreateInput {
	text: String! @validate(constraint: "max=500")
	isAnnounce: Boolean!
	count: Int! = 1 @validate(constraint: "min=1,max=20")
}

input TimerUpdateInput {
	name: String @validate(constraint: "max=50")
	enabled: Boolean
	timeInterval: Int @validate(constraint: "min=1,max=999999")
	messageInterval: Int @validate(constraint: "min=0,max=999999")
	responses: [TimerResponseUpdateInput!]
}

input TimerResponseUpdateInput {
	text: String! @validate(constraint: "max=500")
	isAnnounce: Boolean!
	count: Int! = 1 @validate(constraint: "min=1,max=20")
}
