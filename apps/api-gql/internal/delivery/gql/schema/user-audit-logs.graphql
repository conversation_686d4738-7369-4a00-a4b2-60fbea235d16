extend type Query	{
	auditLog: [AuditLog!]! @isAuthenticated @hasAccessToSelectedDashboard
}

extend type Subscription {
	auditLog: AuditLog! @isAuthenticated @hasAccessToSelectedDashboard
}

type AuditLog {
	system: AuditLogSystem!
	operationType: AuditOperationType!
	oldValue: String
	newValue: String
	objectId: String
	userId: String
	createdAt: Time!

	user: TwirUserTwitchInfo @goField(forceResolver: true)
}

enum AuditLogSystem {
	BADGE
	BADGE_USER
	CHANNEL_COMMAND
	CHANNEL_COMMAND_GROUP
	CHANNEL_VARIABLE
	CHANNEL_GAMES_EIGHT_BALL
	CHANNEL_GAMES_DUEL
	CHANNEL_GAMES_RUSSIAN_ROULETTE
	CHANNEL_GAMES_SEPPUKU
	CHANNEL_GAMES_VOTEBAN
	CHANNEL_GREETING
	CHANNEL_KEYWORD
	CHANNEL_MODERATION_SETTING
	CHANNEL_OVERLAY_CHAT
	CHANNEL_OVERLAY_DUDES
	CHANNEL_OVERLAY_NOW_PLAYING
	CHANNEL_ROLES
	CHANNEL_TIMERS
	CHANNEL_SONG_REQUESTS
	CHANNEL_INTEGRATIONS
	CHANNELS_ALERTS
	CHANNELS_MODULES_SETTINGS
	CHANNELS_CHAT_ALERTS
	CHANNELS_CHAT_TRANSLATION
}
