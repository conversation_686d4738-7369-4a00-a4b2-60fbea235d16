extend type Query {
	authenticatedUser: AuthenticatedUser! @isAuthenticated
	userPublicSettings(userId: String): PublicSettings!
	authLink(redirectTo: String!): String!
	channelUserInfo(userId: String!): ChannelUserInfo! @isAuthenticated
}

extend type Mutation {
	authenticatedUserSelectDashboard(dashboardId: String!): Boolean! @isAuthenticated
	authenticatedUserUpdateSettings(opts: UserUpdateSettingsInput!): Boolean! @isAuthenticated
	authenticatedUserRegenerateApiKey: String! @isAuthenticated
	authenticatedUserUpdatePublicPage(opts: UserUpdatePublicSettingsInput!): Boolean! @isAuthenticated
	logout: Boolean! @isAuthenticated
}

type AuthenticatedUser implements TwirUser {
	id: ID!
	isBotAdmin: Boolean!
	isBanned: Boolean!
	isEnabled: Boolean
	isBotModerator: Boolean
	apiKey: String!
	hideOnLandingPage: Boolean!
	botId: ID
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	selectedDashboardId: String!
	selectedDashboardTwitchUser: TwirUserTwitchInfo! @goField(forceResolver: true)
	availableDashboards: [Dashboard!]! @goField(forceResolver: true)
}

type Dashboard {
	id: String!
	flags: [ChannelRolePermissionEnum!]!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
}

input UserUpdateSettingsInput {
	hideOnLandingPage: Boolean
}

type PublicSettings {
	description: String
	socialLinks: [SocialLink!]!
}

type SocialLink {
	title: String!
	href: String!
}

input UserUpdatePublicSettingsInput {
	description: String @validate(constraint: "max=5000")
	socialLinks: [SocialLinkInput!]
}

input SocialLinkInput {
	title: String! @validate(constraint: "max=50")
	href: String! @validate(constraint: "max=500")
}

type ChannelUserInfo {
	userId: String!
	twitchProfile: TwirUserTwitchInfo! @goField(forceResolver: true)
	isMod: Boolean!
	isVip: Boolean!
	isSubscriber: Boolean!
	watchedMs: Int!
	messages: Int!
	usedEmotes: Int!
	usedChannelPoints: Int!
	followerSince: Time
}
