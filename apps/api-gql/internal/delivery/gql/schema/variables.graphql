extend type Query {
	variables: [Variable!]! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: VIEW_VARIABLES)
	variablesBuiltIn: [BuiltInVariable!]! @isAuthenticated
}

extend type Mutation {
	variablesCreate(opts: VariableCreateInput!): Variable! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_VARIABLES)
	variablesUpdate(id: UUID!, opts: VariableUpdateInput!): Variable! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_VARIABLES)
	variablesDelete(id: UUID!): Boolean! @isAuthenticated @hasAccessToSelectedDashboard @hasChannelRolesDashboardPermission(permission: MANAGE_VARIABLES)
	executeScript(script: String!, language: VariableScriptLanguage!, testAsUserName: String): String! @isAuthenticated
}

enum VariableType {
	SCRIPT
	TEXT
	NUMBER
}

enum VariableScriptLanguage {
	JAVASCRIPT
	PYTHON
}

type Variable {
	id: UUID!
	name: String!
	description: String
	type: VariableType!
	evalValue: String!
	response: String!
	scriptLanguage: VariableScriptLanguage!
}

input VariableCreateInput {
	name: String! @validate(constraint: "max=50")
	description: String	@validate(constraint: "max=500")
	type: VariableType!
	evalValue: String! @validate(constraint: "max=10000")
	response: String! @validate(constraint: "max=500")
	scriptLanguage: VariableScriptLanguage!
}

input VariableUpdateInput {
	name: String @validate(constraint: "max=50")
	description: String @validate(constraint: "max=500")
	type: VariableType
	evalValue: String @validate(constraint: "max=10000")
	response: String @validate(constraint: "max=500")
	scriptLanguage: VariableScriptLanguage
}

type BuiltInVariable {
	name: String!
	example: String!
	description: String!
	visible: Boolean!
	canBeUsedInRegistry: Boolean!
	links: [BuiltInVariableLink!]!
}

type BuiltInVariableLink {
	name: String!
	href: String!
}
