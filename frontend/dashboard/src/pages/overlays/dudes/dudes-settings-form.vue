<script setup lang="ts">
import { toTypedSchema } from '@vee-validate/zod'
import { FontSelector } from '@twir/fontsource'
import { DudesSprite } from '@twir/types/overlays'
import { addZero, capitalize, colorBrightness, hexToRgb } from '@zero-dependency/utils'
import { intervalToDuration } from 'date-fns'
import {
	NColorPicker,
	NDynamicTags,
	NTag,
} from 'naive-ui'
import { useForm } from 'vee-validate'
import { computed, h, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import * as z from 'zod'

import { useDudesForm } from './use-dudes-form.js'
import { useDudesIframe } from './use-dudes-frame.js'

import type { Font } from '@twir/fontsource'

import { useDudesOverlayManager, useProfile, useUserAccessFlagChecker } from '@/api/index.js'
import { Button } from '@/components/ui/button'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/toast'
import { useCopyOverlayLink } from '@/components/overlays/copyOverlayLink.js'
import SelectTwitchUsers from '@/components/twitchUsers/multiple.vue'
import { ChannelRolePermissionEnum } from '@/gql/graphql'

const { t } = useI18n()
const { toast } = useToast()
const { copyOverlayLink } = useCopyOverlayLink('dudes')
const userCanEditOverlays = useUserAccessFlagChecker(ChannelRolePermissionEnum.ManageOverlays)

const { data: profile } = useProfile()
const { data: formValue, reset } = useDudesForm()
const { sendIframeMessage } = useDudesIframe()

// Create validation schema
const formSchema = toTypedSchema(z.object({
	dudeSettings: z.object({
		defaultSprite: z.string(),
		maxOnScreen: z.number().min(0).max(128),
		color: z.string(),
		gravity: z.number().min(100).max(5000),
		maxLifeTime: z.number().min(1000).max(120 * 60 * 1000),
		scale: z.number().min(1).max(10),
		visibleName: z.boolean(),
		soundsEnabled: z.boolean(),
		soundsVolume: z.number().min(0.01).max(1),
		growTime: z.number().min(5000).max(1000 * 60 * 60),
		growMaxScale: z.number().min(1).max(32),
	}),
	ignoreSettings: z.object({
		ignoreCommands: z.boolean(),
		ignoreUsers: z.boolean(),
		users: z.array(z.string()),
	}),
	nameBoxSettings: z.object({
		fill: z.array(z.string()).min(1),
		fillGradientStops: z.array(z.number()),
		fillGradientType: z.number(),
		fontFamily: z.string(),
		fontWeight: z.number(),
		fontStyle: z.string(),
		fontVariant: z.string(),
		fontSize: z.number().min(1).max(128),
		stroke: z.string(),
		strokeThickness: z.number().min(0).max(16),
		lineJoin: z.string(),
		dropShadow: z.boolean(),
		dropShadowColor: z.string(),
		dropShadowAlpha: z.number().min(0).max(1),
		dropShadowBlur: z.number().min(0).max(32),
		dropShadowDistance: z.number().min(0).max(32),
		dropShadowAngle: z.number().min(0).max(Math.PI * 2),
	}),
	messageBoxSettings: z.object({
		enabled: z.boolean(),
		showTime: z.number().min(1000).max(60 * 1000),
		fill: z.string(),
		boxColor: z.string(),
		padding: z.number().min(0).max(64),
		borderRadius: z.number().min(0).max(64),
		fontSize: z.number().min(12).max(64),
	}),
	spitterEmoteSettings: z.object({
		enabled: z.boolean(),
	}),
}))

// Initialize form with current values
const form = useForm({
	validationSchema: formSchema,
	keepValuesOnUnmount: true,
})

// Watch for form changes and update iframe
watch(
	formValue,
	(form) => {
		if (!form) return
		if (!form.nameBoxSettings.fill.length) return
		sendIframeMessage('update-settings', form)
	},
	{ deep: true }
)

watch(
	() => formValue.value.dudeSettings.defaultSprite,
	(dudeSprite) => {
		sendIframeMessage('update-sprite', dudeSprite)
	}
)

watch(
	() => formValue.value.dudeSettings.color,
	(dudeColor) => {
		sendIframeMessage('update-color', dudeColor)
	}
)

const canCopyLink = computed(() => {
	return profile?.value?.selectedDashboardId === profile.value?.id && userCanEditOverlays
})

const manager = useDudesOverlayManager()
const updater = manager.useUpdate()

const onSubmit = form.handleSubmit(async () => {
	if (!formValue.value.id) return

	// Extract the settings without the id for the input
	const { id, ...settings } = formValue.value

	await updater.executeMutation({
		id: formValue.value.id,
		input: settings,
	})

	toast({
		title: t('sharedTexts.saved'),
		duration: 1500,
	})
})

async function save() {
	await onSubmit()
}

function formatDuration(duration: number) {
	const { hours = 0, minutes = 0, seconds = 0 } = intervalToDuration({ start: 0, end: duration })
	if (hours === 0) {
		return `${addZero(minutes)}:${addZero(seconds)}`
	}

	return `${addZero(hours)}:${addZero(minutes)}:${addZero(seconds)}`
}

const fillGradientStops = computed(() => {
	if (!formValue.value) return []
	return formValue.value.nameBoxSettings.fillGradientStops.map((stop) => `${stop}`)
})



const fontData = ref<Font | null>(null)
watch(
	() => fontData.value,
	(font) => {
		if (!font) return
		formValue.value.nameBoxSettings.fontFamily = font.id
		formValue.value.messageBoxSettings.fontFamily = font.id
	}
)

const fontWeightOptions = computed(() => {
	if (!fontData.value) return []
	return fontData.value.weights.map((weight) => ({
		label: `${weight}`,
		value: weight,
	}))
})

const fontStyleOptions = computed(() => {
	if (!fontData.value) return []
	return fontData.value.styles.map((style) => ({
		label: capitalize(style),
		value: style,
	}))
})

const fontVariantOptions = ['normal', 'small-caps'].map((variant) => ({
	label: capitalize(variant),
	value: variant,
}))

const lineJoinOptions = ['round', 'bevel', 'miter'].map((lineJoin) => ({
	label: capitalize(lineJoin),
	value: lineJoin,
}))

const isMessageBoxDisabled = computed(() => {
	return !formValue.value.messageBoxSettings.enabled
})

const isNameBoxDisabled = computed(() => {
	return !formValue.value.dudeSettings.visibleName
})

const isDropShadowDisabled = computed(() => {
	return isNameBoxDisabled.value || !formValue.value.nameBoxSettings.dropShadow
})

const dudesSprites = Object.keys(DudesSprite).map((key) => ({
	label: capitalize(key),
	value: key,
}))
</script>

<template>
	<div v-if="formValue" class="bg-card rounded-lg border">
		<div class="flex flex-wrap gap-2 p-4 border-b">
			<Button variant="destructive" @click="reset">
				{{ t('sharedButtons.setDefaultSettings') }}
			</Button>
			<Button
				variant="secondary"
				:disabled="!formValue.id || !canCopyLink"
				@click="copyOverlayLink({ id: formValue.id! })"
			>
				{{ t('overlays.copyOverlayLink') }}
			</Button>
			<Button @click="save">
				{{ t('sharedButtons.save') }}
			</Button>
		</div>

		<form @submit="onSubmit" class="p-4">
			<Tabs default-value="dude" orientation="vertical" class="flex gap-4">
				<TabsList class="w-48 flex-col h-fit">
					<TabsTrigger value="dude" class="w-full justify-start">
						{{ t('overlays.dudes.dudeDivider') }}
					</TabsTrigger>
					<TabsTrigger value="ignoring" class="w-full justify-start">
						{{ t('overlays.dudes.ignoreDivider') }}
					</TabsTrigger>
					<TabsTrigger value="sounds" class="w-full justify-start">
						{{ t('overlays.dudes.dudeSoundsDivider') }}
					</TabsTrigger>
					<TabsTrigger value="grow" class="w-full justify-start">
						{{ t('overlays.dudes.growDivider') }}
					</TabsTrigger>
					<TabsTrigger value="name-box" class="w-full justify-start">
						{{ t('overlays.dudes.nameBoxDivider') }}
					</TabsTrigger>
					<TabsTrigger value="message-box" class="w-full justify-start">
						{{ t('overlays.dudes.messageBoxDivider') }}
					</TabsTrigger>
					<TabsTrigger value="emote" class="w-full justify-start">
						{{ t('overlays.dudes.emoteDivider') }}
					</TabsTrigger>
				</TabsList>

				<div class="flex-1">
					<TabsContent value="dude" class="space-y-4">
						<FormField v-slot="{ componentField }" name="dudeSettings.defaultSprite">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeDefaultSprite') }}</FormLabel>
								<FormControl>
									<Select v-bind="componentField">
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem v-for="sprite in dudesSprites" :key="sprite.value" :value="sprite.value">
												{{ sprite.label }}
											</SelectItem>
										</SelectContent>
									</Select>
								</FormControl>
								<FormMessage />
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.maxOnScreen">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeMaxOnScreen') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="0"
										:max="128"
										:step="1"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">
									{{ formValue.dudeSettings.maxOnScreen === 0 ? t('overlays.dudes.dudeMaxOnScreenUnlimited') : formValue.dudeSettings.maxOnScreen }}
								</div>
							</FormItem>
						</FormField>

						<FormField name="dudeSettings.color">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeColor') }}</FormLabel>
								<FormControl>
									<NColorPicker v-model:value="formValue.dudeSettings.color" :modes="['hex']" />
								</FormControl>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.gravity">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeGravity') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="100"
										:max="5000"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.dudeSettings.gravity }}</div>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.maxLifeTime">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeMaxLifeTime') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="1000"
										:max="120 * 60 * 1000"
										:step="1000"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formatDuration(formValue.dudeSettings.maxLifeTime) }}</div>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.scale">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeScale') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="1"
										:max="10"
										:step="1"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.dudeSettings.scale }}</div>
							</FormItem>
						</FormField>
					</TabsContent>

					<TabsContent value="ignoring" class="space-y-4">
						<FormField v-slot="{ value, handleChange }" name="ignoreSettings.ignoreCommands">
							<FormItem class="flex items-center justify-between space-y-0">
								<FormLabel>{{ t('overlays.dudes.ignoreCommands') }}</FormLabel>
								<FormControl>
									<Switch :checked="value" @update:checked="handleChange" />
								</FormControl>
							</FormItem>
						</FormField>

						<FormField v-slot="{ value, handleChange }" name="ignoreSettings.ignoreUsers">
							<FormItem class="flex items-center justify-between space-y-0">
								<FormLabel>{{ t('overlays.dudes.ignoreUsers') }}</FormLabel>
								<FormControl>
									<Switch :checked="value" @update:checked="handleChange" />
								</FormControl>
							</FormItem>
						</FormField>

						<FormField name="ignoreSettings.users">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.ignoreUsersList') }}</FormLabel>
								<FormControl>
									<SelectTwitchUsers v-model="formValue.ignoreSettings.users" />
								</FormControl>
							</FormItem>
						</FormField>
					</TabsContent>

					<TabsContent value="sounds" class="space-y-4">
						<FormField v-slot="{ value, handleChange }" name="dudeSettings.soundsEnabled">
							<FormItem class="flex items-center justify-between space-y-0">
								<FormLabel>{{ t('overlays.dudes.enable') }}</FormLabel>
								<FormControl>
									<Switch :checked="value" @update:checked="handleChange" />
								</FormControl>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.soundsVolume">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.dudeSoundsVolume') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="0.01"
										:max="1"
										:step="0.01"
										:disabled="!formValue.dudeSettings.soundsEnabled"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">
									{{ `${(formValue.dudeSettings.soundsVolume * 100).toFixed(0)}%` }}
								</div>
							</FormItem>
						</FormField>
					</TabsContent>

					<TabsContent value="grow" class="space-y-4">
						<FormField v-slot="{ componentField }" name="dudeSettings.growTime">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.growTime') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="5000"
										:max="1000 * 60 * 60"
										:step="1000"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formatDuration(formValue.dudeSettings.growTime) }}</div>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="dudeSettings.growMaxScale">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.growMaxScale') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="formValue.dudeSettings.scale + 1"
										:max="32"
										:step="1"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.dudeSettings.growMaxScale }}</div>
							</FormItem>
						</FormField>
					</TabsContent>

					<TabsContent value="name-box">
						<ScrollArea class="h-[calc(62vh-var(--layout-header-height))] pr-4">
							<div class="space-y-4">
								<FormField v-slot="{ value, handleChange }" name="dudeSettings.visibleName">
									<FormItem class="flex items-center justify-between space-y-0">
										<FormLabel>{{ t('overlays.dudes.enable') }}</FormLabel>
										<FormControl>
											<Switch :checked="value" @update:checked="handleChange" />
										</FormControl>
									</FormItem>
								</FormField>

								<FormField name="nameBoxSettings.fill">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFill') }}</FormLabel>
										<FormControl>
											<NDynamicTags
												v-model:value="formValue.nameBoxSettings.fill"
												:disabled="isNameBoxDisabled"
												:max="6"
												:render-tag="
													(tag: string, index: number) => {
														const rgb = hexToRgb(tag)
														const textColor = rgb && colorBrightness(rgb) > 128 ? '#000' : '#fff'

														return h(
															NTag,
															{
																closable: true,
																onClose: () => {
																	formValue.nameBoxSettings.fill.splice(index, 1)
																},
																style: {
																	'--n-close-icon-color': textColor,
																	'--n-close-icon-color-hover': textColor,
																},
																color: {
																	color: tag,
																	borderColor: tag,
																	textColor,
																},
															},
															{ default: () => tag }
														)
													}
												"
											>
												<template #input="{ submit, deactivate }">
													<NColorPicker
														style="width: 80px"
														size="small"
														default-show
														:show-alpha="false"
														:modes="['hex']"
														:actions="['confirm']"
														@confirm="submit($event)"
														@update-show="deactivate"
														@blur="deactivate"
													/>
												</template>
											</NDynamicTags>
										</FormControl>
										<FormMessage />
									</FormItem>
								</FormField>

								<FormField name="nameBoxSettings.fillGradientStops">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFillGradientStops') }}</FormLabel>
										<FormControl>
											<NDynamicTags
												v-model:value="fillGradientStops"
												:disabled="isNameBoxDisabled"
												:render-tag="
													(tag: string, index: number) => {
														return h(
															NTag,
															{
																closable: true,
																onClose: () => {
																	formValue.nameBoxSettings.fillGradientStops.splice(index, 1)
																},
															},
															{ default: () => tag }
														)
													}
												"
												:max="formValue.nameBoxSettings.fill.length"
												@update:value="
													(values: string[]) => {
														formValue.nameBoxSettings.fillGradientStops = values.map(Number)
													}
												"
											>
												<template #input="{ submit, deactivate }">
													<Input
														type="number"
														class="w-24"
														placeholder="0.1"
														:max="1"
														:min="0"
														:step="0.01"
														@keyup.enter="submit($event.target.value)"
														@blur="deactivate"
													/>
												</template>
											</NDynamicTags>
										</FormControl>
										<FormMessage />
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.fillGradientType">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxGradientType') }}</FormLabel>
										<FormControl>
											<Select v-bind="componentField" :disabled="isNameBoxDisabled || formValue.nameBoxSettings.fill.length < 2">
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem value="0">Vertical</SelectItem>
													<SelectItem value="1">Horizontal</SelectItem>
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField name="nameBoxSettings.fontFamily">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFontFamily') }}</FormLabel>
										<FormControl>
											<FontSelector
												v-model:font="fontData"
												:disabled="isNameBoxDisabled"
												:font-family="formValue.nameBoxSettings.fontFamily"
												:font-weight="formValue.nameBoxSettings.fontWeight"
												:font-style="formValue.nameBoxSettings.fontStyle"
												:subsets="['latin', 'cyrillic']"
											/>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.fontWeight">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFontWeight') }}</FormLabel>
										<FormControl>
											<Select v-bind="componentField" :disabled="isNameBoxDisabled">
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem v-for="option in fontWeightOptions" :key="option.value" :value="String(option.value)">
														{{ option.label }}
													</SelectItem>
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.fontStyle">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFontStyle') }}</FormLabel>
										<FormControl>
											<Select v-bind="componentField" :disabled="isNameBoxDisabled">
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem v-for="option in fontStyleOptions" :key="option.value" :value="option.value">
														{{ option.label }}
													</SelectItem>
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.fontVariant">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFontVariant') }}</FormLabel>
										<FormControl>
											<Select v-bind="componentField" :disabled="isNameBoxDisabled">
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem v-for="option in fontVariantOptions" :key="option.value" :value="option.value">
														{{ option.label }}
													</SelectItem>
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.fontSize">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxFontSize') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:disabled="isNameBoxDisabled"
												:min="1"
												:max="128"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ formValue.nameBoxSettings.fontSize }}</div>
									</FormItem>
								</FormField>

								<FormField name="nameBoxSettings.stroke">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxStroke') }}</FormLabel>
										<FormControl>
											<NColorPicker
												v-model:value="formValue.nameBoxSettings.stroke"
												:disabled="isNameBoxDisabled"
												:modes="['hex']"
											/>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.strokeThickness">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameStrokeThickness') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:disabled="isNameBoxDisabled"
												:min="0"
												:max="16"
												:step="1"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ formValue.nameBoxSettings.strokeThickness }}</div>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.lineJoin">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxLineJoin') }}</FormLabel>
										<FormControl>
											<Select v-bind="componentField" :disabled="isNameBoxDisabled">
												<SelectTrigger>
													<SelectValue />
												</SelectTrigger>
												<SelectContent>
													<SelectItem v-for="option in lineJoinOptions" :key="option.value" :value="option.value">
														{{ option.label }}
													</SelectItem>
												</SelectContent>
											</Select>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ value, handleChange }" name="nameBoxSettings.dropShadow">
									<FormItem class="flex items-center justify-between space-y-0">
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadow') }}</FormLabel>
										<FormControl>
											<Switch :checked="value" :disabled="isNameBoxDisabled" @update:checked="handleChange" />
										</FormControl>
									</FormItem>
								</FormField>

								<FormField name="nameBoxSettings.dropShadowColor">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadowColor') }}</FormLabel>
										<FormControl>
											<NColorPicker
												v-model:value="formValue.nameBoxSettings.dropShadowColor"
												:modes="['hex']"
												:disabled="isDropShadowDisabled"
											/>
										</FormControl>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.dropShadowAlpha">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadowAlpha') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:min="0"
												:max="1"
												:step="0.01"
												:disabled="isDropShadowDisabled"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ formValue.nameBoxSettings.dropShadowAlpha }}</div>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.dropShadowBlur">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadowBlur') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:min="0"
												:max="32"
												:step="0.1"
												:disabled="isDropShadowDisabled"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ formValue.nameBoxSettings.dropShadowBlur }}</div>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.dropShadowDistance">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadowDistance') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:min="0"
												:max="32"
												:step="0.1"
												:disabled="isDropShadowDisabled"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ formValue.nameBoxSettings.dropShadowDistance }}</div>
									</FormItem>
								</FormField>

								<FormField v-slot="{ componentField }" name="nameBoxSettings.dropShadowAngle">
									<FormItem>
										<FormLabel>{{ t('overlays.dudes.nameBoxDropShadowAngle') }}</FormLabel>
										<FormControl>
											<Slider
												v-bind="componentField"
												:min="0"
												:max="Math.PI * 2"
												:step="0.01"
												:disabled="isDropShadowDisabled"
												class="w-full"
											/>
										</FormControl>
										<div class="text-sm text-muted-foreground">{{ `${Math.round((formValue.nameBoxSettings.dropShadowAngle * 180) / Math.PI)}°` }}</div>
									</FormItem>
								</FormField>
							</div>
						</ScrollArea>
					</TabsContent>

					<TabsContent value="message-box" class="space-y-4">
						<FormField v-slot="{ value, handleChange }" name="messageBoxSettings.enabled">
							<FormItem class="flex items-center justify-between space-y-0">
								<FormLabel>{{ t('overlays.dudes.enable') }}</FormLabel>
								<FormControl>
									<Switch :checked="value" @update:checked="handleChange" />
								</FormControl>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="messageBoxSettings.showTime">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxShowTime') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="1000"
										:max="60 * 1000"
										:step="1000"
										:disabled="isMessageBoxDisabled"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ `${Math.round(formValue.messageBoxSettings.showTime / 1000)}s` }}</div>
							</FormItem>
						</FormField>

						<FormField name="messageBoxSettings.fill">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxFill') }}</FormLabel>
								<FormControl>
									<NColorPicker
										v-model:value="formValue.messageBoxSettings.fill"
										:modes="['hex']"
										:disabled="isMessageBoxDisabled"
									/>
								</FormControl>
							</FormItem>
						</FormField>

						<FormField name="messageBoxSettings.boxColor">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxBackground') }}</FormLabel>
								<FormControl>
									<NColorPicker
										v-model:value="formValue.messageBoxSettings.boxColor"
										:modes="['hex']"
										:disabled="isMessageBoxDisabled"
									/>
								</FormControl>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="messageBoxSettings.padding">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxPadding') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="0"
										:max="64"
										:step="1"
										:disabled="isMessageBoxDisabled"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.messageBoxSettings.padding }}</div>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="messageBoxSettings.borderRadius">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxBorderRadius') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="0"
										:max="64"
										:step="1"
										:disabled="isMessageBoxDisabled"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.messageBoxSettings.borderRadius }}</div>
							</FormItem>
						</FormField>

						<FormField v-slot="{ componentField }" name="messageBoxSettings.fontSize">
							<FormItem>
								<FormLabel>{{ t('overlays.dudes.messageBoxFontSize') }}</FormLabel>
								<FormControl>
									<Slider
										v-bind="componentField"
										:min="12"
										:max="64"
										:step="1"
										:disabled="isMessageBoxDisabled"
										class="w-full"
									/>
								</FormControl>
								<div class="text-sm text-muted-foreground">{{ formValue.messageBoxSettings.fontSize }}</div>
							</FormItem>
						</FormField>
					</TabsContent>

					<TabsContent value="emote" class="space-y-4">
						<FormField v-slot="{ value, handleChange }" name="spitterEmoteSettings.enabled">
							<FormItem class="flex items-center justify-between space-y-0">
								<FormLabel>{{ t('overlays.dudes.enable') }}</FormLabel>
								<FormControl>
									<Switch :checked="value" @update:checked="handleChange" />
								</FormControl>
							</FormItem>
						</FormField>
					</TabsContent>
				</div>
			</Tabs>
		</form>
	</div>
</template>

<style scoped>
@import '../styles.css';
</style>
