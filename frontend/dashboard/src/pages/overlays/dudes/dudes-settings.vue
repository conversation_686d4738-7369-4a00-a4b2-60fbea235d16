<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'

import DudesSettingsForm from './dudes-settings-form.vue'
import { useDudesForm } from './use-dudes-form.js'
import { useDudesIframe } from './use-dudes-frame.js'

import type { DudesSettingsWithOptionalId } from './dudes-settings.js'

import { useDudesOverlayManager, useProfile, useUserAccessFlagChecker } from '@/api/index.js'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useNaiveDiscrete } from '@/composables/use-naive-discrete.js'
import CommandButton from '@/features/commands/ui/command-button.vue'
import { ChannelRolePermissionEnum } from '@/gql/graphql'

const userCanEditOverlays = useUserAccessFlagChecker(ChannelRolePermissionEnum.ManageOverlays)
const dudesOverlayManager = useDudesOverlayManager()
const creator = dudesOverlayManager.useCreate()
const deleter = dudesOverlayManager.useDelete()
const { data: profile } = useProfile()

const { t } = useI18n()
const { dialog } = useNaiveDiscrete()

const { data: entities } = dudesOverlayManager.useGetAll()

const openedTab = ref<string>()

const { dudesIframe, sendIframeMessage } = useDudesIframe()
const dudesIframeUrl = computed(() => {
	if (!profile.value || !openedTab.value) return null
	return `${window.location.origin}/overlays/${profile.value.apiKey}/dudes?id=${openedTab.value}`
})

const { setData, getDefaultSettings } = useDudesForm()

function resetTab() {
	if (!entities.value?.dudesGetAll?.at(0)) {
		openedTab.value = undefined
		return
	}

	openedTab.value = entities.value.dudesGetAll.at(0)?.id
}

watch(
	entities,
	() => {
		resetTab()
	},
	{ immediate: true }
)

watch(openedTab, (v) => {
	const entity = entities.value?.dudesGetAll?.find((s) => s.id === v) as DudesSettingsWithOptionalId
	if (!entity) return
	setData(entity)
})

async function handleClose(id: string) {
	dialog.create({
		title: 'Delete preset',
		content: 'Are you sure you want to delete this preset?',
		positiveText: 'Delete',
		negativeText: 'Cancel',
		showIcon: false,
		onPositiveClick: async () => {
			const entity = entities.value?.dudesGetAll?.find((s) => s.id === id)
			if (!entity?.id) return

			await deleter.executeMutation({ id: entity.id })
			resetTab()
		},
	})
}

async function handleAdd() {
	const { id: _id, ...rest } = getDefaultSettings()
	console.log(rest)
	await creator.executeMutation({ input: rest })
}

const addable = computed(() => {
	return userCanEditOverlays.value && (entities.value?.dudesGetAll?.length ?? 0) < 5
})
</script>

<template>
	<div class="flex gap-10 h-[calc(100%-var(--layout-header-height))]">
		<div class="relative w-[70%]">
			<div v-if="dudesIframeUrl">
				<iframe ref="dudesIframe" class="absolute iframe" :src="dudesIframeUrl" />
				<div class="absolute top-[18px] left-2 flex gap-1.5">
					<Button size="sm" @click="sendIframeMessage('spawn-emote')">Emote</Button>
					<Button size="sm" @click="sendIframeMessage('show-message')">Message</Button>
					<Button size="sm" @click="sendIframeMessage('jump')">Jump</Button>
					<Button size="sm" @click="sendIframeMessage('grow')">Grow</Button>
					<Button size="sm" @click="sendIframeMessage('leave')">Leave</Button>
					<Button size="sm" @click="sendIframeMessage('reset')">Reset</Button>
				</div>
			</div>
		</div>
		<div class="w-[30%]">
			<div class="flex gap-2 flex-wrap">
				<CommandButton title="Jump command" name="jump" />
				<CommandButton title="Color command" name="dudes color" />
				<CommandButton title="Grow command" name="dudes grow" />
				<CommandButton title="Sprite command" name="dudes sprite" />
				<CommandButton title="Leave command" name="dudes leave" />
			</div>

			<div class="mt-4">
				<div class="mb-2 text-sm font-medium">
					{{ t('overlays.chat.presets') }}
				</div>

				<Tabs v-if="entities?.dudesGetAll?.length" v-model="openedTab" class="w-full">
					<div class="flex items-center justify-between mb-4">
						<TabsList class="grid w-full grid-cols-5 gap-1">
							<div
								v-for="(entity, entityIndex) in entities?.dudesGetAll?.slice(0, 5)"
								:key="entity.id"
								class="relative group"
							>
								<TabsTrigger
									:value="entity.id!"
									class="text-xs w-full pr-6"
								>
									#{{ entityIndex + 1 }}
								</TabsTrigger>
								<Button
									v-if="userCanEditOverlays && entities?.dudesGetAll?.length > 1"
									size="sm"
									variant="ghost"
									class="absolute right-0 top-0 h-full w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
									@click="handleClose(entity.id!)"
								>
									×
								</Button>
							</div>
						</TabsList>
						<Button
							v-if="addable"
							size="sm"
							variant="outline"
							@click="handleAdd"
						>
							+
						</Button>
					</div>

					<TabsContent
						v-for="entity in entities?.dudesGetAll"
						:key="entity.id"
						:value="entity.id!"
						class="mt-0"
					>
						<DudesSettingsForm />
					</TabsContent>
				</Tabs>

				<Alert v-if="!entities?.dudesGetAll?.length" class="mt-2">
					<AlertDescription>
						Create new overlay for edit settings
					</AlertDescription>
				</Alert>
			</div>
		</div>
	</div>
</template>

<style scoped>
@import '../styles.css';

.iframe {
	height: 100%;
	width: 100%;
	aspect-ratio: 16 / 9;
	border: 0;
	margin-top: 8px;
	@apply border border-border rounded-lg;
}
</style>
