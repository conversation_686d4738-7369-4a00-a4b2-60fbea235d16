// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: discord/discord.proto

package discord

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ChannelType int32

const (
	ChannelType_VOICE ChannelType = 0
	ChannelType_TEXT  ChannelType = 1
)

// Enum value maps for ChannelType.
var (
	ChannelType_name = map[int32]string{
		0: "VOICE",
		1: "TEXT",
	}
	ChannelType_value = map[string]int32{
		"VOICE": 0,
		"TEXT":  1,
	}
)

func (x ChannelType) Enum() *ChannelType {
	p := new(ChannelType)
	*p = x
	return p
}

func (x ChannelType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChannelType) Descriptor() protoreflect.EnumDescriptor {
	return file_discord_discord_proto_enumTypes[0].Descriptor()
}

func (ChannelType) Type() protoreflect.EnumType {
	return &file_discord_discord_proto_enumTypes[0]
}

func (x ChannelType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChannelType.Descriptor instead.
func (ChannelType) EnumDescriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{0}
}

type GetGuildChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GuildId       string                 `protobuf:"bytes,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildChannelsRequest) Reset() {
	*x = GetGuildChannelsRequest{}
	mi := &file_discord_discord_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildChannelsRequest) ProtoMessage() {}

func (x *GetGuildChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetGuildChannelsRequest) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{0}
}

func (x *GetGuildChannelsRequest) GetGuildId() string {
	if x != nil {
		return x.GuildId
	}
	return ""
}

type GuildChannel struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name            string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Type            ChannelType            `protobuf:"varint,3,opt,name=type,proto3,enum=discord.ChannelType" json:"type,omitempty"`
	CanSendMessages bool                   `protobuf:"varint,4,opt,name=can_send_messages,json=canSendMessages,proto3" json:"can_send_messages,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GuildChannel) Reset() {
	*x = GuildChannel{}
	mi := &file_discord_discord_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GuildChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuildChannel) ProtoMessage() {}

func (x *GuildChannel) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuildChannel.ProtoReflect.Descriptor instead.
func (*GuildChannel) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{1}
}

func (x *GuildChannel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GuildChannel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GuildChannel) GetType() ChannelType {
	if x != nil {
		return x.Type
	}
	return ChannelType_VOICE
}

func (x *GuildChannel) GetCanSendMessages() bool {
	if x != nil {
		return x.CanSendMessages
	}
	return false
}

type GetGuildChannelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Channels      []*GuildChannel        `protobuf:"bytes,1,rep,name=channels,proto3" json:"channels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildChannelsResponse) Reset() {
	*x = GetGuildChannelsResponse{}
	mi := &file_discord_discord_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildChannelsResponse) ProtoMessage() {}

func (x *GetGuildChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildChannelsResponse.ProtoReflect.Descriptor instead.
func (*GetGuildChannelsResponse) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{2}
}

func (x *GetGuildChannelsResponse) GetChannels() []*GuildChannel {
	if x != nil {
		return x.Channels
	}
	return nil
}

type GetGuildInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GuildId       string                 `protobuf:"bytes,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildInfoRequest) Reset() {
	*x = GetGuildInfoRequest{}
	mi := &file_discord_discord_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildInfoRequest) ProtoMessage() {}

func (x *GetGuildInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildInfoRequest.ProtoReflect.Descriptor instead.
func (*GetGuildInfoRequest) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{3}
}

func (x *GetGuildInfoRequest) GetGuildId() string {
	if x != nil {
		return x.GuildId
	}
	return ""
}

type GetGuildInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Icon          string                 `protobuf:"bytes,3,opt,name=icon,proto3" json:"icon,omitempty"`
	Channels      []*GuildChannel        `protobuf:"bytes,4,rep,name=channels,proto3" json:"channels,omitempty"`
	Roles         []*Role                `protobuf:"bytes,5,rep,name=roles,proto3" json:"roles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildInfoResponse) Reset() {
	*x = GetGuildInfoResponse{}
	mi := &file_discord_discord_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildInfoResponse) ProtoMessage() {}

func (x *GetGuildInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildInfoResponse.ProtoReflect.Descriptor instead.
func (*GetGuildInfoResponse) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{4}
}

func (x *GetGuildInfoResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *GetGuildInfoResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetGuildInfoResponse) GetIcon() string {
	if x != nil {
		return x.Icon
	}
	return ""
}

func (x *GetGuildInfoResponse) GetChannels() []*GuildChannel {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *GetGuildInfoResponse) GetRoles() []*Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

type LeaveGuildRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GuildId       string                 `protobuf:"bytes,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LeaveGuildRequest) Reset() {
	*x = LeaveGuildRequest{}
	mi := &file_discord_discord_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LeaveGuildRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LeaveGuildRequest) ProtoMessage() {}

func (x *LeaveGuildRequest) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LeaveGuildRequest.ProtoReflect.Descriptor instead.
func (*LeaveGuildRequest) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{5}
}

func (x *LeaveGuildRequest) GetGuildId() string {
	if x != nil {
		return x.GuildId
	}
	return ""
}

type GetGuildRolesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GuildId       string                 `protobuf:"bytes,1,opt,name=guild_id,json=guildId,proto3" json:"guild_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildRolesRequest) Reset() {
	*x = GetGuildRolesRequest{}
	mi := &file_discord_discord_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildRolesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildRolesRequest) ProtoMessage() {}

func (x *GetGuildRolesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildRolesRequest.ProtoReflect.Descriptor instead.
func (*GetGuildRolesRequest) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{6}
}

func (x *GetGuildRolesRequest) GetGuildId() string {
	if x != nil {
		return x.GuildId
	}
	return ""
}

type Role struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Color         string                 `protobuf:"bytes,3,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Role) Reset() {
	*x = Role{}
	mi := &file_discord_discord_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Role) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Role) ProtoMessage() {}

func (x *Role) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Role.ProtoReflect.Descriptor instead.
func (*Role) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{7}
}

func (x *Role) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Role) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Role) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

type GetGuildRolesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Roles         []*Role                `protobuf:"bytes,1,rep,name=roles,proto3" json:"roles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGuildRolesResponse) Reset() {
	*x = GetGuildRolesResponse{}
	mi := &file_discord_discord_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGuildRolesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGuildRolesResponse) ProtoMessage() {}

func (x *GetGuildRolesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_discord_discord_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGuildRolesResponse.ProtoReflect.Descriptor instead.
func (*GetGuildRolesResponse) Descriptor() ([]byte, []int) {
	return file_discord_discord_proto_rawDescGZIP(), []int{8}
}

func (x *GetGuildRolesResponse) GetRoles() []*Role {
	if x != nil {
		return x.Roles
	}
	return nil
}

var File_discord_discord_proto protoreflect.FileDescriptor

const file_discord_discord_proto_rawDesc = "" +
	"\n" +
	"\x15discord/discord.proto\x12\adiscord\x1a\x1bgoogle/protobuf/empty.proto\"4\n" +
	"\x17GetGuildChannelsRequest\x12\x19\n" +
	"\bguild_id\x18\x01 \x01(\tR\aguildId\"\x88\x01\n" +
	"\fGuildChannel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12(\n" +
	"\x04type\x18\x03 \x01(\x0e2\x14.discord.ChannelTypeR\x04type\x12*\n" +
	"\x11can_send_messages\x18\x04 \x01(\bR\x0fcanSendMessages\"M\n" +
	"\x18GetGuildChannelsResponse\x121\n" +
	"\bchannels\x18\x01 \x03(\v2\x15.discord.GuildChannelR\bchannels\"0\n" +
	"\x13GetGuildInfoRequest\x12\x19\n" +
	"\bguild_id\x18\x01 \x01(\tR\aguildId\"\xa6\x01\n" +
	"\x14GetGuildInfoResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04icon\x18\x03 \x01(\tR\x04icon\x121\n" +
	"\bchannels\x18\x04 \x03(\v2\x15.discord.GuildChannelR\bchannels\x12#\n" +
	"\x05roles\x18\x05 \x03(\v2\r.discord.RoleR\x05roles\".\n" +
	"\x11LeaveGuildRequest\x12\x19\n" +
	"\bguild_id\x18\x01 \x01(\tR\aguildId\"1\n" +
	"\x14GetGuildRolesRequest\x12\x19\n" +
	"\bguild_id\x18\x01 \x01(\tR\aguildId\"@\n" +
	"\x04Role\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x14\n" +
	"\x05color\x18\x03 \x01(\tR\x05color\"<\n" +
	"\x15GetGuildRolesResponse\x12#\n" +
	"\x05roles\x18\x01 \x03(\v2\r.discord.RoleR\x05roles*\"\n" +
	"\vChannelType\x12\t\n" +
	"\x05VOICE\x10\x00\x12\b\n" +
	"\x04TEXT\x10\x012\xc7\x02\n" +
	"\aDiscord\x12W\n" +
	"\x10GetGuildChannels\x12 .discord.GetGuildChannelsRequest\x1a!.discord.GetGuildChannelsResponse\x12M\n" +
	"\fGetGuildInfo\x12\x1c.discord.GetGuildInfoRequest\x1a\x1d.discord.GetGuildInfoResponse\"\x00\x12B\n" +
	"\n" +
	"LeaveGuild\x12\x1a.discord.LeaveGuildRequest\x1a\x16.google.protobuf.Empty\"\x00\x12P\n" +
	"\rGetGuildRoles\x12\x1d.discord.GetGuildRolesRequest\x1a\x1e.discord.GetGuildRolesResponse\"\x00B+Z)github.com/twirapp/twir/libs/grpc/discordb\x06proto3"

var (
	file_discord_discord_proto_rawDescOnce sync.Once
	file_discord_discord_proto_rawDescData []byte
)

func file_discord_discord_proto_rawDescGZIP() []byte {
	file_discord_discord_proto_rawDescOnce.Do(func() {
		file_discord_discord_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_discord_discord_proto_rawDesc), len(file_discord_discord_proto_rawDesc)))
	})
	return file_discord_discord_proto_rawDescData
}

var file_discord_discord_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_discord_discord_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_discord_discord_proto_goTypes = []any{
	(ChannelType)(0),                 // 0: discord.ChannelType
	(*GetGuildChannelsRequest)(nil),  // 1: discord.GetGuildChannelsRequest
	(*GuildChannel)(nil),             // 2: discord.GuildChannel
	(*GetGuildChannelsResponse)(nil), // 3: discord.GetGuildChannelsResponse
	(*GetGuildInfoRequest)(nil),      // 4: discord.GetGuildInfoRequest
	(*GetGuildInfoResponse)(nil),     // 5: discord.GetGuildInfoResponse
	(*LeaveGuildRequest)(nil),        // 6: discord.LeaveGuildRequest
	(*GetGuildRolesRequest)(nil),     // 7: discord.GetGuildRolesRequest
	(*Role)(nil),                     // 8: discord.Role
	(*GetGuildRolesResponse)(nil),    // 9: discord.GetGuildRolesResponse
	(*emptypb.Empty)(nil),            // 10: google.protobuf.Empty
}
var file_discord_discord_proto_depIdxs = []int32{
	0,  // 0: discord.GuildChannel.type:type_name -> discord.ChannelType
	2,  // 1: discord.GetGuildChannelsResponse.channels:type_name -> discord.GuildChannel
	2,  // 2: discord.GetGuildInfoResponse.channels:type_name -> discord.GuildChannel
	8,  // 3: discord.GetGuildInfoResponse.roles:type_name -> discord.Role
	8,  // 4: discord.GetGuildRolesResponse.roles:type_name -> discord.Role
	1,  // 5: discord.Discord.GetGuildChannels:input_type -> discord.GetGuildChannelsRequest
	4,  // 6: discord.Discord.GetGuildInfo:input_type -> discord.GetGuildInfoRequest
	6,  // 7: discord.Discord.LeaveGuild:input_type -> discord.LeaveGuildRequest
	7,  // 8: discord.Discord.GetGuildRoles:input_type -> discord.GetGuildRolesRequest
	3,  // 9: discord.Discord.GetGuildChannels:output_type -> discord.GetGuildChannelsResponse
	5,  // 10: discord.Discord.GetGuildInfo:output_type -> discord.GetGuildInfoResponse
	10, // 11: discord.Discord.LeaveGuild:output_type -> google.protobuf.Empty
	9,  // 12: discord.Discord.GetGuildRoles:output_type -> discord.GetGuildRolesResponse
	9,  // [9:13] is the sub-list for method output_type
	5,  // [5:9] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_discord_discord_proto_init() }
func file_discord_discord_proto_init() {
	if File_discord_discord_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_discord_discord_proto_rawDesc), len(file_discord_discord_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_discord_discord_proto_goTypes,
		DependencyIndexes: file_discord_discord_proto_depIdxs,
		EnumInfos:         file_discord_discord_proto_enumTypes,
		MessageInfos:      file_discord_discord_proto_msgTypes,
	}.Build()
	File_discord_discord_proto = out.File
	file_discord_discord_proto_goTypes = nil
	file_discord_discord_proto_depIdxs = nil
}
